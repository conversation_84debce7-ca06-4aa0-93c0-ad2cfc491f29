#!/usr/bin/env python3
"""
Bayesian Timing Predictor with Uncertainty Intervals
===================================================

Based on Grok 4 Recommendation: "Consider Bayesian integration for uncertainty"

Implements probabilistic framework for market timing predictions with:
1. Prior distributions for session open timing (based on market microstructure)
2. Likelihood functions based on session parameters
3. Posterior predictions with confidence intervals
4. Adaptive learning from observed timing data

Key Advantage: Provides uncertainty quantification instead of point estimates.
"""

import json
import numpy as np
from scipy import stats
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
class BayesianTimingPredictor:
    """
    Bayesian approach to timing prediction with uncertainty quantification.
    """
    
    def __init__(self):
        # Prior beliefs about session open timing (based on market research)
        self.session_open_prior = {
            "mean": 0.8,      # Most events within first minute
            "std": 1.2,       # Some variation possible
            "min_time": 0.0,  # Can't be before session start
            "max_time": 5.0   # Extreme upper bound for session opens
        }
        
        # Historical data for updating priors
        self.observed_timings = []
        self.session_contexts = []
        
    def session_open_prior_distribution(self) -> Tuple[float, float]:
        """
        Define prior distribution for session open timing.
        
        Based on market microstructure research:
        - Most events occur 0-1 minutes after open
        - Some delayed events up to 3-5 minutes
        - Gamma distribution fits this pattern well
        """
        # Gamma distribution parameters for session opens
        # Shape=1.5, Scale=0.8 gives mean≈1.2, most probability 0-2 minutes
        alpha = 1.5  # Shape parameter
        beta = 1.875  # Rate parameter (1/scale)
        
        return alpha, beta
    
    def likelihood_function(self, session_distance: float, volatility_index: float,
                          gamma_enhanced: float, fvg_proximity: float) -> Tuple[float, float]:
        """
        Calculate likelihood parameters based on session characteristics.
        
        Returns adjustment factors for prior distribution.
        """
        
        # Distance effect on timing uncertainty
        distance_factor = 1.0 + 0.3 * np.log(session_distance + 1)
        
        # Volatility effect (higher volatility = more immediate events)
        volatility_factor = 1.0 - 0.4 * min(volatility_index, 0.5)
        
        # Momentum effect (higher gamma = more immediate breakouts)  
        momentum_factor = 1.0 - 0.2 * max(0, gamma_enhanced - 2.0)
        
        # FVG proximity (closer gaps = more precise timing)
        fvg_factor = 1.0 - 0.3 * fvg_proximity
        
        # Combined likelihood adjustments
        mean_adjustment = volatility_factor * momentum_factor * fvg_factor
        variance_adjustment = distance_factor
        
        return mean_adjustment, variance_adjustment
    
    def bayesian_prediction(self, session_distance: float, volatility_index: float,
                          gamma_enhanced: float, fvg_proximity: float,
                          confidence_level: float = 0.95) -> Dict:
        """
        Make Bayesian prediction with uncertainty intervals.
        """
        
        # Get prior distribution parameters
        prior_alpha, prior_beta = self.session_open_prior_distribution()
        
        # Calculate likelihood adjustments
        mean_adj, var_adj = self.likelihood_function(
            session_distance, volatility_index, gamma_enhanced, fvg_proximity
        )
        
        # Update parameters based on likelihood
        posterior_alpha = prior_alpha * mean_adj
        posterior_beta = prior_beta / var_adj
        
        # Posterior distribution statistics
        posterior_mean = posterior_alpha / posterior_beta
        posterior_variance = posterior_alpha / (posterior_beta ** 2)
        posterior_std = np.sqrt(posterior_variance)
        
        # Calculate confidence intervals
        alpha_level = 1 - confidence_level
        lower_quantile = alpha_level / 2
        upper_quantile = 1 - alpha_level / 2
        
        # Gamma distribution quantiles
        lower_bound = stats.gamma.ppf(lower_quantile, posterior_alpha, scale=1/posterior_beta)
        upper_bound = stats.gamma.ppf(upper_quantile, posterior_alpha, scale=1/posterior_beta)
        
        # Mode (most likely value)
        mode = max(0, (posterior_alpha - 1) / posterior_beta) if posterior_alpha > 1 else 0
        
        return {
            "prediction_type": "bayesian_probabilistic",
            "posterior_mean": posterior_mean,
            "posterior_mode": mode,
            "posterior_std": posterior_std,
            "confidence_interval": {
                "level": confidence_level,
                "lower_bound": max(0, lower_bound),
                "upper_bound": min(5.0, upper_bound)  # Cap at reasonable max
            },
            "distribution_parameters": {
                "alpha": posterior_alpha,
                "beta": posterior_beta
            },
            "likelihood_adjustments": {
                "mean_factor": mean_adj,
                "variance_factor": var_adj
            }
        }
    
    def update_with_observation(self, observed_time: float, session_distance: float,
                              volatility_index: float, gamma_enhanced: float,
                              fvg_proximity: float):
        """
        Update Bayesian model with new observation (online learning).
        """
        
        # Store observation with context
        self.observed_timings.append(observed_time)
        self.session_contexts.append({
            "session_distance": session_distance,
            "volatility_index": volatility_index,
            "gamma_enhanced": gamma_enhanced,
            "fvg_proximity": fvg_proximity
        })
        
        # Limit history to prevent overflow
        if len(self.observed_timings) > 100:
            self.observed_timings = self.observed_timings[-50:]
            self.session_contexts = self.session_contexts[-50:]
    
    def adaptive_prediction(self, session_distance: float, volatility_index: float,
                          gamma_enhanced: float, fvg_proximity: float,
                          use_historical: bool = True) -> Dict:
        """
        Make prediction using adaptive Bayesian approach with historical data.
        """
        
        base_prediction = self.bayesian_prediction(
            session_distance, volatility_index, gamma_enhanced, fvg_proximity
        )
        
        if not use_historical or len(self.observed_timings) < 3:
            return base_prediction
        
        # Incorporate historical observations
        historical_mean = np.mean(self.observed_timings)
        historical_std = np.std(self.observed_timings)
        
        # Weighted combination of prior and historical data
        n_obs = len(self.observed_timings)
        weight_historical = min(0.7, n_obs / 20)  # Increase weight with more data
        weight_prior = 1 - weight_historical
        
        # Combine predictions
        combined_mean = (weight_prior * base_prediction["posterior_mean"] + 
                        weight_historical * historical_mean)
        
        combined_std = np.sqrt(weight_prior * base_prediction["posterior_std"]**2 + 
                              weight_historical * historical_std**2)
        
        # Update confidence interval
        z_score = stats.norm.ppf(0.975)  # 95% confidence
        new_lower = max(0, combined_mean - z_score * combined_std)
        new_upper = combined_mean + z_score * combined_std
        
        base_prediction.update({
            "adaptive_mean": combined_mean,
            "adaptive_std": combined_std,
            "adaptive_confidence_interval": {
                "level": 0.95,
                "lower_bound": new_lower,
                "upper_bound": new_upper
            },
            "historical_data": {
                "n_observations": n_obs,
                "historical_mean": historical_mean,
                "weight_used": weight_historical
            }
        })
        
        return base_prediction
    
    def validate_bayesian_approach(self, validation_data: List[Dict]) -> Dict:
        """
        Validate Bayesian approach with uncertainty quantification.
        """
        results = []
        coverage_count = 0
        mean_errors = []
        mode_errors = []
        
        for case in validation_data:
            params = case['source_parameters']
            actual_time = case['actual_expansion_minutes']
            
            # Make Bayesian prediction
            prediction = self.bayesian_prediction(
                case['session_distance'],
                params['volatility_index'],
                params['gamma_enhanced'],
                params['fvg_proximity']
            )
            
            # Calculate errors
            mean_error = abs(prediction['posterior_mean'] - actual_time)
            mode_error = abs(prediction['posterior_mode'] - actual_time)
            
            # Check if actual falls within confidence interval
            ci = prediction['confidence_interval']
            within_ci = ci['lower_bound'] <= actual_time <= ci['upper_bound']
            if within_ci:
                coverage_count += 1
            
            # Update model with observation
            self.update_with_observation(
                actual_time, case['session_distance'],
                params['volatility_index'], params['gamma_enhanced'],
                params['fvg_proximity']
            )
            
            mean_errors.append(mean_error)
            mode_errors.append(mode_error)
            
            results.append({
                "pair_name": case['pair_name'],
                "actual_time": float(actual_time),
                "predicted_mean": float(prediction['posterior_mean']),
                "predicted_mode": float(prediction['posterior_mode']),
                "confidence_interval": f"{ci['lower_bound']:.2f}-{ci['upper_bound']:.2f}",
                "within_ci": bool(within_ci),
                "mean_error": float(mean_error),
                "mode_error": float(mode_error)
            })
        
        coverage_percentage = (coverage_count / len(validation_data)) * 100
        avg_mean_error = np.mean(mean_errors)
        avg_mode_error = np.mean(mode_errors)
        
        return {
            "validation_results": results,
            "summary": {
                "coverage_percentage": float(coverage_percentage),
                "avg_mean_error": float(avg_mean_error),
                "avg_mode_error": float(avg_mode_error),
                "total_cases": len(validation_data),
                "recommended_estimator": "mode" if avg_mode_error < avg_mean_error else "mean"
            }
        }

def main():
    """Test Bayesian timing predictor."""
    
    # Load validation data
    try:
        with open('/Users/<USER>/grok-claude-automation/enhanced_formula_raw_validation_results.json', 'r') as f:
            validation_data = json.load(f)
    except FileNotFoundError:
        print("Validation data not found")
        return
    
    # Initialize Bayesian predictor
    predictor = BayesianTimingPredictor()
    
    # Run validation
    results = predictor.validate_bayesian_approach(validation_data['validation_results'])
    
    print(f"\n🎯 Bayesian Timing Predictor Results")
    print(f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    print(f"Coverage: {results['summary']['coverage_percentage']:.1f}% within confidence intervals")
    print(f"Mean Error: {results['summary']['avg_mean_error']:.2f} minutes")
    print(f"Mode Error: {results['summary']['avg_mode_error']:.2f} minutes") 
    print(f"Recommended Estimator: {results['summary']['recommended_estimator']}")
    
    # Compare with previous approaches
    try:
        with open('/Users/<USER>/grok-claude-automation/recalibrated_formula_results.json', 'r') as f:
            recalibrated_data = json.load(f)
            recalibrated_error = recalibrated_data['comparison_vs_enhanced']['recalibrated_avg_error']
            
        improvement = ((recalibrated_error - results['summary']['avg_mode_error']) / 
                      recalibrated_error) * 100
        print(f"Improvement vs Recalibrated: {improvement:.1f}%")
        
    except FileNotFoundError:
        pass
    
    # Save results
    output_file = "/Users/<USER>/grok-claude-automation/bayesian_timing_results.json"
    with open(output_file, 'w') as f:
        json.dump({
            "bayesian_metadata": {
                "implementation_date": datetime.now().isoformat(),
                "approach": "gamma_prior_with_likelihood_updates",
                "confidence_level": 0.95,
                "adaptive_learning": True
            },
            "results": results
        }, f, indent=2)
    
    print(f"📁 Results saved to: {output_file}")

if __name__ == "__main__":
    main()