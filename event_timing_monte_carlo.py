#!/usr/bin/env python3
"""
Event Timing Monte Carlo System
TRANSFORMATION: Generates 1000 event timing sequences instead of price paths.
Predicts WHEN events occur, not WHERE price moves.
"""

import numpy as np
import pandas as pd
from scipy import stats
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import logging
import json

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
try:
    from market_state_hmm import MarketState, MarketStateHMM
    from options_expiry_integration import OptionsExpiryIntegration
except ImportError as e:
    print(f"⚠️ Import warning: {e}")

class EventType(Enum):
    """Market event types for timing simulation"""
    CASCADE_START = "cascade_start"
    EXPANSION_PHASE = "expansion_phase"
    CONSOLIDATION_BEGIN = "consolidation_begin"
    LIQUIDITY_SWEEP = "liquidity_sweep"
    FVG_INTERACTION = "fvg_interaction"
    REVERSAL_SETUP = "reversal_setup"
    MOMENTUM_ACCELERATION = "momentum_acceleration"
    ENERGY_EXHAUSTION = "energy_exhaustion"
    HALF_HOUR_MAGNETISM = "half_hour_magnetism"

@dataclass
class TimingEvent:
    """Individual timing event in a sequence"""
    event_id: str
    event_type: EventType
    predicted_time: datetime
    confidence: float
    duration_minutes: float
    trigger_conditions: Dict[str, float]
    preceding_events: List[str]
    market_state: MarketState

@dataclass
class EventSequence:
    """Complete sequence of events for one simulation path"""
    sequence_id: int
    events: List[TimingEvent]
    session_start: datetime
    session_character: str
    total_events: int
    sequence_probability: float
    energy_profile: List[float]  # Energy levels throughout sequence
    state_transitions: List[MarketState]
    timing_accuracy_estimate: float

@dataclass
class TimingSimulationResult:
    """Result from Monte Carlo timing simulation"""
    simulation_metadata: Dict[str, Any]
    sequences: List[EventSequence]
    event_probability_distribution: Dict[EventType, float]
    timing_percentiles: Dict[str, Dict[str, float]]  # event_type -> {p10, p25, p50, p75, p90}
    consensus_timing_predictions: Dict[EventType, Dict[str, Any]]
    confidence_intervals: Dict[EventType, Tuple[datetime, datetime]]
    session_timing_profile: Dict[str, Any]

class EventTimingGenerator:
    """Generates individual event timing sequences"""
    
    def __init__(self, session_data: dict, tracker_context: tuple = None):
        self.session_data = session_data
        self.tracker_context = tracker_context
        
        # Extract session parameters
        session_metadata = session_data.get('session_metadata', {})
        if 'original_session_data' in session_data:
            session_metadata = session_data['original_session_data']['session_metadata']
            
        self.session_duration_minutes = session_metadata.get('duration_minutes', 300)
        self.session_type = session_metadata.get('session_type', 'unknown')
        
        # Extract price and character data
        price_data = session_data.get('price_data', {})
        if 'original_session_data' in session_data:
            price_data = session_data['original_session_data']['price_data']
            
        self.session_character = price_data.get('session_character', 'unknown')
        self.session_range = price_data.get('range', 100)
        
        # Extract energy data
        grok_data = session_data.get('grok_enhanced_calculations', {})
        unit_b = grok_data.get('unit_b_energy_structure', {})
        energy_accumulation = unit_b.get('energy_accumulation', {})
        
        self.base_energy_rate = energy_accumulation.get('energy_rate', 0.5)
        self.total_accumulated_energy = energy_accumulation.get('total_accumulated', 150.0)
        
        # Extract T_memory from tracker context
        self.t_memory = 5.0
        if tracker_context:
            try:
                htf_tracker, fvg_tracker, liquidity_tracker = tracker_context
                self.t_memory = fvg_tracker.get('t_memory_calculations', {}).get('t_memory_current', 5.0)
            except Exception:
                pass
        
        # Initialize state-based parameters
        self.hmm = MarketStateHMM()
        self.options_integration = OptionsExpiryIntegration()
        
        # Event timing defaults (minutes from session start)
        self.base_event_timings = {
            EventType.CASCADE_START: (20, 35),       # Range of possible times
            EventType.EXPANSION_PHASE: (15, 45), 
            EventType.CONSOLIDATION_BEGIN: (40, 80),
            EventType.LIQUIDITY_SWEEP: (5, 25),
            EventType.FVG_INTERACTION: (25, 60),
            EventType.REVERSAL_SETUP: (30, 70),
            EventType.MOMENTUM_ACCELERATION: (10, 40),
            EventType.ENERGY_EXHAUSTION: (60, 120),
            EventType.HALF_HOUR_MAGNETISM: (25, 35)  # Around half-hour marks
        }
        
        # Event dependencies (which events typically follow others)
        self.event_dependencies = {
            EventType.CASCADE_START: [EventType.MOMENTUM_ACCELERATION, EventType.EXPANSION_PHASE],
            EventType.EXPANSION_PHASE: [EventType.ENERGY_EXHAUSTION, EventType.CONSOLIDATION_BEGIN],
            EventType.LIQUIDITY_SWEEP: [EventType.CASCADE_START, EventType.REVERSAL_SETUP],
            EventType.MOMENTUM_ACCELERATION: [EventType.CASCADE_START, EventType.EXPANSION_PHASE],
            EventType.CONSOLIDATION_BEGIN: [EventType.FVG_INTERACTION, EventType.REVERSAL_SETUP],
            EventType.ENERGY_EXHAUSTION: [EventType.CONSOLIDATION_BEGIN, EventType.REVERSAL_SETUP],
            EventType.FVG_INTERACTION: [EventType.MOMENTUM_ACCELERATION, EventType.REVERSAL_SETUP],
            EventType.REVERSAL_SETUP: [EventType.LIQUIDITY_SWEEP, EventType.CASCADE_START]
        }
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def generate_event_sequence(self, sequence_id: int, session_start: datetime) -> EventSequence:
        """Generate a single event timing sequence"""
        
        events = []
        energy_profile = []
        state_transitions = []
        
        # Initialize sequence state
        current_energy = self.base_energy_rate
        current_state = MarketState.CONSOLIDATING
        current_time = session_start
        
        # Track sequence probability
        sequence_probability = 1.0
        
        # Generate events throughout session duration
        elapsed_minutes = 0
        
        while elapsed_minutes < self.session_duration_minutes:
            # Update energy profile
            energy_profile.append(current_energy)
            state_transitions.append(current_state)
            
            # Determine next event based on current state and energy
            next_event = self._select_next_event(
                current_state, current_energy, elapsed_minutes, events
            )
            
            if next_event:
                # Calculate event timing with variations
                event_timing = self._calculate_event_timing(
                    next_event, elapsed_minutes, current_energy, current_state
                )
                
                # Apply options expiry effects
                enhanced_timing = self._apply_options_timing_effects(
                    event_timing, next_event, session_start
                )
                
                # Create timing event
                timing_event = TimingEvent(
                    event_id=f"seq{sequence_id}_evt{len(events)}",
                    event_type=next_event,
                    predicted_time=enhanced_timing['enhanced_time'],
                    confidence=enhanced_timing['confidence'],
                    duration_minutes=self._calculate_event_duration(next_event),
                    trigger_conditions={
                        'energy_threshold': current_energy,
                        't_memory': self.t_memory,
                        'state_requirement': current_state.value
                    },
                    preceding_events=[e.event_id for e in events[-2:] if events],
                    market_state=current_state
                )
                
                events.append(timing_event)
                
                # Update sequence probability
                sequence_probability *= enhanced_timing['confidence']
                
                # Update state and energy based on event
                current_state, current_energy = self._update_state_after_event(
                    next_event, current_state, current_energy
                )
                
                # Advance time
                elapsed_minutes = (enhanced_timing['enhanced_time'] - session_start).total_seconds() / 60
                current_time = enhanced_timing['enhanced_time']
            else:
                # No event selected, advance time
                elapsed_minutes += 10  # 10-minute steps when no events
                current_time += timedelta(minutes=10)
                
                # Gradual energy decay
                current_energy *= 0.98
        
        # Calculate timing accuracy estimate
        timing_accuracy = self._estimate_sequence_accuracy(events)
        
        return EventSequence(
            sequence_id=sequence_id,
            events=events,
            session_start=session_start,
            session_character=self.session_character,
            total_events=len(events),
            sequence_probability=sequence_probability,
            energy_profile=energy_profile,
            state_transitions=state_transitions,
            timing_accuracy_estimate=timing_accuracy
        )
    
    def _select_next_event(self, current_state: MarketState, energy_level: float,
                          elapsed_minutes: float, existing_events: List[TimingEvent]) -> Optional[EventType]:
        """Select next event based on current market conditions"""
        
        # Event probabilities based on current state
        state_event_probabilities = {
            MarketState.CONSOLIDATING: {
                EventType.LIQUIDITY_SWEEP: 0.3,
                EventType.FVG_INTERACTION: 0.25,
                EventType.MOMENTUM_ACCELERATION: 0.2,
                EventType.CASCADE_START: 0.1,
                EventType.CONSOLIDATION_BEGIN: 0.15
            },
            MarketState.PRE_CASCADE: {
                EventType.CASCADE_START: 0.5,
                EventType.MOMENTUM_ACCELERATION: 0.3,
                EventType.LIQUIDITY_SWEEP: 0.15,
                EventType.EXPANSION_PHASE: 0.05
            },
            MarketState.EXPANDING: {
                EventType.EXPANSION_PHASE: 0.4,
                EventType.MOMENTUM_ACCELERATION: 0.25,
                EventType.ENERGY_EXHAUSTION: 0.2,
                EventType.CASCADE_START: 0.1,
                EventType.REVERSAL_SETUP: 0.05
            },
            MarketState.EXHAUSTED: {
                EventType.ENERGY_EXHAUSTION: 0.4,
                EventType.CONSOLIDATION_BEGIN: 0.3,
                EventType.REVERSAL_SETUP: 0.2,
                EventType.FVG_INTERACTION: 0.1
            }
        }
        
        base_probabilities = state_event_probabilities.get(current_state, {})
        
        # Adjust probabilities based on energy level
        adjusted_probabilities = {}
        for event_type, prob in base_probabilities.items():
            energy_modifier = 1.0
            
            # High energy events
            if event_type in [EventType.CASCADE_START, EventType.EXPANSION_PHASE, EventType.MOMENTUM_ACCELERATION]:
                energy_modifier = 1.0 + (energy_level - 0.5) * 0.8  # Up to 40% boost for high energy
                
            # Low energy events
            elif event_type in [EventType.CONSOLIDATION_BEGIN, EventType.ENERGY_EXHAUSTION]:
                energy_modifier = 1.0 + (0.5 - energy_level) * 0.6  # Up to 30% boost for low energy
            
            adjusted_probabilities[event_type] = max(0.01, prob * energy_modifier)
        
        # Apply half-hour magnetism boost
        minutes_in_hour = elapsed_minutes % 60
        near_half_hour = abs(minutes_in_hour - 30) <= 5 or minutes_in_hour <= 5 or minutes_in_hour >= 55
        
        if near_half_hour:
            adjusted_probabilities[EventType.HALF_HOUR_MAGNETISM] = 0.2
        
        # Check for event dependencies (don't repeat recent events)
        recent_event_types = set(e.event_type for e in existing_events[-3:])
        for recent_type in recent_event_types:
            if recent_type in adjusted_probabilities:
                adjusted_probabilities[recent_type] *= 0.3  # Reduce probability of recent events
        
        # Normalize probabilities
        total_prob = sum(adjusted_probabilities.values())
        if total_prob <= 0:
            return None
            
        normalized_probs = {k: v/total_prob for k, v in adjusted_probabilities.items()}
        
        # Random selection based on probabilities
        rand = np.random.random()
        cumulative_prob = 0.0
        
        for event_type, prob in normalized_probs.items():
            cumulative_prob += prob
            if rand <= cumulative_prob:
                return event_type
        
        return None
    
    def _calculate_event_timing(self, event_type: EventType, elapsed_minutes: float,
                               energy_level: float, current_state: MarketState) -> datetime:
        """Calculate timing for an event with variations"""
        
        # Get base timing range
        min_time, max_time = self.base_event_timings.get(event_type, (30, 60))
        
        # Add random variation within range
        base_timing = np.random.uniform(min_time, max_time)
        
        # Apply energy-based adjustments
        if event_type in [EventType.CASCADE_START, EventType.EXPANSION_PHASE]:
            # High energy events happen sooner
            energy_adjustment = -(energy_level - 0.5) * 10
        elif event_type in [EventType.CONSOLIDATION_BEGIN, EventType.ENERGY_EXHAUSTION]:
            # Low energy events happen later
            energy_adjustment = (energy_level - 0.5) * 8
        else:
            energy_adjustment = 0
        
        # Apply T_memory influence (higher T_memory = faster events)
        t_memory_adjustment = -(self.t_memory - 5.0) * 1.5
        
        # Apply session character modifiers
        character_adjustment = 0
        if 'expansion' in self.session_character.lower():
            if event_type in [EventType.CASCADE_START, EventType.EXPANSION_PHASE]:
                character_adjustment = -5  # Expansion events happen 5 min sooner
        elif 'consolidation' in self.session_character.lower():
            if event_type in [EventType.CONSOLIDATION_BEGIN, EventType.FVG_INTERACTION]:
                character_adjustment = -3  # Consolidation events happen 3 min sooner
        
        # Calculate final timing
        final_timing = base_timing + energy_adjustment + t_memory_adjustment + character_adjustment
        
        # Ensure event doesn't happen in the past or too far in future
        final_timing = max(elapsed_minutes + 2, min(final_timing, self.session_duration_minutes - 10))
        
        return final_timing
    
    def _apply_options_timing_effects(self, event_timing_minutes: float, event_type: EventType,
                                    session_start: datetime) -> Dict[str, Any]:
        """Apply options expiry timing effects to event"""
        
        predicted_time = session_start + timedelta(minutes=event_timing_minutes)
        
        # Use options integration for enhancement
        enhancement = self.options_integration.enhance_event_timing_prediction(
            predicted_time, event_type.value, 0.7  # Base confidence
        )
        
        return {
            'original_time': predicted_time,
            'enhanced_time': enhancement['enhanced_prediction_time'],
            'confidence': enhancement['enhanced_confidence'],
            'adjustment_minutes': enhancement['time_adjustment_minutes'],
            'expiry_effects': enhancement['expiry_factors']
        }
    
    def _calculate_event_duration(self, event_type: EventType) -> float:
        """Calculate expected duration of an event"""
        
        duration_ranges = {
            EventType.CASCADE_START: (2, 8),        # Quick cascade formation
            EventType.EXPANSION_PHASE: (10, 30),    # Longer expansion periods
            EventType.CONSOLIDATION_BEGIN: (15, 45), # Extended consolidation
            EventType.LIQUIDITY_SWEEP: (1, 5),      # Quick liquidity events
            EventType.FVG_INTERACTION: (3, 12),     # Moderate FVG events
            EventType.REVERSAL_SETUP: (5, 15),      # Setup phases
            EventType.MOMENTUM_ACCELERATION: (3, 10), # Quick momentum bursts
            EventType.ENERGY_EXHAUSTION: (8, 20),   # Exhaustion periods
            EventType.HALF_HOUR_MAGNETISM: (2, 6)   # Brief magnetism effects
        }
        
        min_duration, max_duration = duration_ranges.get(event_type, (5, 15))
        return np.random.uniform(min_duration, max_duration)
    
    def _update_state_after_event(self, event_type: EventType, current_state: MarketState,
                                 current_energy: float) -> Tuple[MarketState, float]:
        """Update market state and energy after an event"""
        
        new_state = current_state
        new_energy = current_energy
        
        # State transitions based on events
        if event_type == EventType.CASCADE_START:
            new_state = MarketState.EXPANDING
            new_energy = min(1.0, current_energy + 0.3)
            
        elif event_type == EventType.EXPANSION_PHASE:
            new_state = MarketState.EXPANDING
            new_energy = min(1.0, current_energy + 0.2)
            
        elif event_type == EventType.CONSOLIDATION_BEGIN:
            new_state = MarketState.CONSOLIDATING
            new_energy = max(0.1, current_energy - 0.2)
            
        elif event_type == EventType.ENERGY_EXHAUSTION:
            new_state = MarketState.EXHAUSTED
            new_energy = max(0.1, current_energy - 0.4)
            
        elif event_type == EventType.MOMENTUM_ACCELERATION:
            if current_state == MarketState.CONSOLIDATING:
                new_state = MarketState.PRE_CASCADE
            new_energy = min(1.0, current_energy + 0.15)
            
        elif event_type == EventType.LIQUIDITY_SWEEP:
            if current_energy > 0.6:
                new_state = MarketState.PRE_CASCADE
            new_energy = min(1.0, current_energy + 0.1)
            
        # Add some randomness to prevent deterministic sequences
        energy_noise = np.random.normal(0, 0.05)
        new_energy = max(0.05, min(0.95, new_energy + energy_noise))
        
        return new_state, new_energy
    
    def _estimate_sequence_accuracy(self, events: List[TimingEvent]) -> float:
        """Estimate the timing accuracy of this sequence"""
        
        # Base accuracy starts high
        base_accuracy = 0.8
        
        # Factors that affect accuracy
        # 1. Number of events (more events = lower accuracy)
        event_count_penalty = len(events) * 0.02
        
        # 2. Event complexity (cascade events are harder to time)
        complexity_penalty = 0
        for event in events:
            if event.event_type in [EventType.CASCADE_START, EventType.EXPANSION_PHASE]:
                complexity_penalty += 0.03
            elif event.event_type == EventType.HALF_HOUR_MAGNETISM:
                complexity_penalty -= 0.01  # These are easier to time
        
        # 3. Session character influence
        character_bonus = 0
        if 'expansion' in self.session_character.lower():
            character_bonus = 0.05  # Expansion sessions are more predictable
        elif 'consolidation' in self.session_character.lower():
            character_bonus = 0.03
        
        # Calculate final accuracy
        estimated_accuracy = base_accuracy - event_count_penalty - complexity_penalty + character_bonus
        
        return max(0.1, min(0.95, estimated_accuracy))

class EventTimingMonteCarloSimulator:
    """Main Monte Carlo simulator for event timing"""
    
    def __init__(self, session_data: dict, tracker_context: tuple = None):
        self.session_data = session_data
        self.tracker_context = tracker_context
        self.event_generator = EventTimingGenerator(session_data, tracker_context)
        
        # Simulation parameters
        self.num_simulations = 1000
        self.confidence_levels = [0.1, 0.25, 0.5, 0.75, 0.9]  # For percentiles
        
    def run_timing_simulation(self, session_start: datetime = None) -> TimingSimulationResult:
        """Run complete Monte Carlo timing simulation"""
        
        print("🎲 EVENT TIMING MONTE CARLO SIMULATION")
        print("=" * 50)
        print(f"Session: {self.event_generator.session_type}")
        print(f"Character: {self.event_generator.session_character}")
        print(f"Simulations: {self.num_simulations}")
        
        if session_start is None:
            session_start = datetime.now().replace(second=0, microsecond=0)
        
        # Generate all sequences
        sequences = []
        print(f"\n🔄 Generating {self.num_simulations} event timing sequences...")
        
        for i in range(self.num_simulations):
            if i % 100 == 0:
                print(f"   Progress: {i}/{self.num_simulations} sequences")
                
            sequence = self.event_generator.generate_event_sequence(i, session_start)
            sequences.append(sequence)
        
        print(f"✅ Generated {len(sequences)} sequences")
        
        # Analyze results
        analysis_results = self._analyze_sequences(sequences)
        
        # Build final result
        result = TimingSimulationResult(
            simulation_metadata={
                'session_type': self.event_generator.session_type,
                'session_character': self.event_generator.session_character,
                'session_start': session_start.isoformat(),
                'num_simulations': len(sequences),
                'simulation_timestamp': datetime.now().isoformat()
            },
            sequences=sequences,
            event_probability_distribution=analysis_results['event_probabilities'],
            timing_percentiles=analysis_results['timing_percentiles'],
            consensus_timing_predictions=analysis_results['consensus_predictions'],
            confidence_intervals=analysis_results['confidence_intervals'],
            session_timing_profile=analysis_results['timing_profile']
        )
        
        self._display_simulation_results(result)
        
        return result
    
    def _analyze_sequences(self, sequences: List[EventSequence]) -> Dict[str, Any]:
        """Analyze simulation sequences to extract timing patterns"""
        
        print(f"\n📊 Analyzing {len(sequences)} sequences...")
        
        # Collect all events by type
        events_by_type = {}
        for sequence in sequences:
            for event in sequence.events:
                if event.event_type not in events_by_type:
                    events_by_type[event.event_type] = []
                events_by_type[event.event_type].append(event)
        
        # Calculate event probabilities
        event_probabilities = {}
        for event_type, events in events_by_type.items():
            probability = len(events) / len(sequences)  # How often this event occurs
            event_probabilities[event_type] = probability
        
        # Calculate timing percentiles for each event type
        timing_percentiles = {}
        for event_type, events in events_by_type.items():
            if len(events) < 10:  # Skip events with too few occurrences
                continue
                
            # Extract timing in minutes from session start
            timings = [(event.predicted_time - sequences[0].session_start).total_seconds() / 60 
                      for event in events]
            
            percentiles = {}
            for p in [10, 25, 50, 75, 90]:
                percentiles[f'p{p}'] = np.percentile(timings, p)
            
            timing_percentiles[event_type] = percentiles
        
        # Generate consensus predictions
        consensus_predictions = {}
        for event_type, events in events_by_type.items():
            if len(events) < 10:
                continue
                
            timings = [(event.predicted_time - sequences[0].session_start).total_seconds() / 60 
                      for event in events]
            confidences = [event.confidence for event in events]
            
            consensus_predictions[event_type] = {
                'median_timing_minutes': np.median(timings),
                'mean_confidence': np.mean(confidences),
                'occurrence_probability': len(events) / len(sequences),
                'timing_std_dev': np.std(timings),
                'timing_range': (np.min(timings), np.max(timings))
            }
        
        # Calculate confidence intervals
        confidence_intervals = {}
        for event_type, events in events_by_type.items():
            if len(events) < 10:
                continue
                
            timings = [(event.predicted_time - sequences[0].session_start).total_seconds() / 60 
                      for event in events]
            
            # 90% confidence interval
            lower_bound = sequences[0].session_start + timedelta(minutes=np.percentile(timings, 5))
            upper_bound = sequences[0].session_start + timedelta(minutes=np.percentile(timings, 95))
            
            confidence_intervals[event_type] = (lower_bound, upper_bound)
        
        # Session timing profile
        timing_profile = {
            'average_events_per_session': np.mean([len(seq.events) for seq in sequences]),
            'most_common_first_event': self._find_most_common_first_event(sequences),
            'typical_event_sequence': self._find_typical_sequence(sequences),
            'session_energy_pattern': self._analyze_energy_patterns(sequences),
            'state_transition_frequency': self._analyze_state_transitions(sequences)
        }
        
        return {
            'event_probabilities': event_probabilities,
            'timing_percentiles': timing_percentiles,
            'consensus_predictions': consensus_predictions,
            'confidence_intervals': confidence_intervals,
            'timing_profile': timing_profile
        }
    
    def _find_most_common_first_event(self, sequences: List[EventSequence]) -> str:
        """Find the most common first event across sequences"""
        
        first_events = [seq.events[0].event_type.value for seq in sequences if seq.events]
        if not first_events:
            return "none"
            
        from collections import Counter
        return Counter(first_events).most_common(1)[0][0]
    
    def _find_typical_sequence(self, sequences: List[EventSequence]) -> List[str]:
        """Find the most typical event sequence pattern"""
        
        # Extract event type sequences
        event_sequences = []
        for seq in sequences:
            event_types = [event.event_type.value for event in seq.events[:5]]  # First 5 events
            event_sequences.append(tuple(event_types))
        
        if not event_sequences:
            return []
            
        from collections import Counter
        most_common_sequence = Counter(event_sequences).most_common(1)[0][0]
        return list(most_common_sequence)
    
    def _analyze_energy_patterns(self, sequences: List[EventSequence]) -> Dict[str, float]:
        """Analyze energy patterns across sequences"""
        
        if not sequences or not sequences[0].energy_profile:
            return {}
        
        # Get average energy profile
        profile_length = min(len(seq.energy_profile) for seq in sequences if seq.energy_profile)
        
        avg_energy_profile = []
        for i in range(profile_length):
            energy_values = [seq.energy_profile[i] for seq in sequences if len(seq.energy_profile) > i]
            avg_energy_profile.append(np.mean(energy_values))
        
        return {
            'initial_energy': avg_energy_profile[0] if avg_energy_profile else 0.5,
            'peak_energy': max(avg_energy_profile) if avg_energy_profile else 0.5,
            'final_energy': avg_energy_profile[-1] if avg_energy_profile else 0.5,
            'energy_volatility': np.std(avg_energy_profile) if avg_energy_profile else 0.0
        }
    
    def _analyze_state_transitions(self, sequences: List[EventSequence]) -> Dict[str, int]:
        """Analyze state transition frequencies"""
        
        transition_counts = {}
        
        for sequence in sequences:
            for i in range(len(sequence.state_transitions) - 1):
                current_state = sequence.state_transitions[i].value
                next_state = sequence.state_transitions[i + 1].value
                
                transition = f"{current_state} → {next_state}"
                transition_counts[transition] = transition_counts.get(transition, 0) + 1
        
        return transition_counts
    
    def _display_simulation_results(self, result: TimingSimulationResult):
        """Display simulation results summary"""
        
        print(f"\n🎯 SIMULATION RESULTS SUMMARY")
        print(f"=" * 40)
        
        print(f"📈 Event Probabilities:")
        for event_type, probability in result.event_probability_distribution.items():
            if probability > 0.1:  # Only show events with >10% probability
                print(f"   {event_type.value}: {probability:.1%}")
        
        print(f"\n⏰ Consensus Timing Predictions:")
        for event_type, prediction in result.consensus_timing_predictions.items():
            median_time = prediction['median_timing_minutes']
            confidence = prediction['mean_confidence']
            print(f"   {event_type.value}: {median_time:.0f} min (confidence: {confidence:.2f})")
        
        print(f"\n📊 Session Profile:")
        profile = result.session_timing_profile
        print(f"   Avg events per session: {profile['average_events_per_session']:.1f}")
        print(f"   Most common first event: {profile['most_common_first_event']}")
        print(f"   Typical sequence: {' → '.join(profile['typical_event_sequence'][:3])}")

def main():
    """Test event timing Monte Carlo simulation"""
    
    print("🧪 EVENT TIMING MONTE CARLO TEST")
    print("=" * 45)
    
    # Load test data
    try:
        with open('ny_pm_grokEnhanced_2025_07_23.json', 'r') as f:
            session_data = json.load(f)
        with open('HTF_Context_Lunch_grokEnhanced_2025_07_23.json', 'r') as f:
            htf_tracker = json.load(f)
        with open('FVG_State_Lunch_grokEnhanced_2025_07_23.json', 'r') as f:
            fvg_tracker = json.load(f)
        with open('Liquidity_State_Lunch_grokEnhanced_2025_07_23.json', 'r') as f:
            liquidity_tracker = json.load(f)
            
        tracker_context = (htf_tracker, fvg_tracker, liquidity_tracker)
        
    except FileNotFoundError as e:
        print(f"❌ Test data not found: {e}")
        return
    
    # Initialize simulator
    simulator = EventTimingMonteCarloSimulator(session_data, tracker_context)
    
    # Set session start time
    session_start = datetime(2025, 7, 23, 13, 30)  # NY PM session start
    
    # Run simulation
    results = simulator.run_timing_simulation(session_start)
    
    # Save results
    results_data = {
        'simulation_metadata': results.simulation_metadata,
        'event_probabilities': {k.value: v for k, v in results.event_probability_distribution.items()},
        'consensus_predictions': {k.value: v for k, v in results.consensus_timing_predictions.items()},
        'session_profile': results.session_timing_profile
    }
    
    output_file = f"event_timing_monte_carlo_{datetime.now().strftime('%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(results_data, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to: {output_file}")
    
    print(f"\n🏆 MONTE CARLO TRANSFORMATION SUCCESS:")
    print(f"   Generated {len(results.sequences)} event timing sequences")
    print(f"   Predicted WHEN events occur, not WHERE price goes ✅")
    print(f"   Event timing accuracy: Focus on temporal precision")

if __name__ == "__main__":
    main()