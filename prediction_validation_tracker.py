#!/usr/bin/env python3
"""
HMM-Monte Carlo Prediction Validation Tracker
Tracks prediction accuracy and refines the integration system based on results.
"""

import json
from datetime import datetime
from typing import Dict, Any
from dataclasses import dataclass, asdict

@dataclass
class PredictionRecord:
    """Record of a prediction and its validation"""
    prediction_date: str
    target_session: str
    predicted_state: str
    state_confidence: float
    energy_rate_forecast: float
    momentum_strength_forecast: float
    price_targets: Dict[str, float]
    timing_windows: Dict[str, tuple]
    integration_score: float
    
    # Validation fields (filled when actual data available)
    actual_state: str = None
    actual_energy_rate: float = None
    actual_momentum_strength: float = None
    actual_price_targets: Dict[str, float] = None
    actual_timing_events: Dict[str, float] = None
    
    # Accuracy metrics
    state_prediction_correct: bool = None
    energy_rate_error_percent: float = None
    momentum_strength_error_percent: float = None
    timing_accuracy_score: float = None
    overall_accuracy_score: float = None

class PredictionValidator:
    """Validates and tracks HMM-Monte Carlo prediction performance"""
    
    def __init__(self, log_file: str = "hmm_monte_carlo_predictions.json"):
        self.log_file = log_file
        self.predictions = self._load_predictions()
    
    def _load_predictions(self) -> list:
        """Load existing predictions from log file"""
        try:
            with open(self.log_file, 'r') as f:
                data = json.load(f)
                return data.get('predictions', [])
        except FileNotFoundError:
            return []
    
    def _save_predictions(self):
        """Save predictions to log file"""
        data = {
            "predictions": self.predictions,
            "last_updated": datetime.now().isoformat(),
            "total_predictions": len(self.predictions)
        }
        with open(self.log_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def log_prediction(self, prediction_result: Dict[str, Any], target_session: str):
        """Log a new prediction for future validation"""
        
        record = PredictionRecord(
            prediction_date=datetime.now().isoformat(),
            target_session=target_session,
            predicted_state=prediction_result['predicted_state'].value,
            state_confidence=prediction_result['state_confidence'],
            energy_rate_forecast=prediction_result['energy_rate_forecast'],
            momentum_strength_forecast=prediction_result['momentum_strength_forecast'],
            price_targets=prediction_result['price_targets'],
            timing_windows={k: list(v) for k, v in prediction_result['timing_windows'].items()},
            integration_score=prediction_result['hmm_monte_carlo_integration_score']
        )
        
        self.predictions.append(asdict(record))
        self._save_predictions()
        
        print(f"✅ Prediction logged for {target_session}")
        print(f"📊 Total predictions tracked: {len(self.predictions)}")
    
    def validate_prediction(self, prediction_id: int, actual_session_file: str):
        """Validate a prediction against actual session results"""
        
        if prediction_id >= len(self.predictions):
            print(f"❌ Prediction {prediction_id} not found")
            return
        
        try:
            # Load actual session data
            with open(actual_session_file, 'r') as f:
                actual_data = json.load(f)
            
            # Extract actual enhanced metrics
            grok_calcs = actual_data['grok_enhanced_calculations']
            unit_b = grok_calcs['unit_b_energy_structure']
            unit_c = grok_calcs['unit_c_advanced_dynamics']
            
            actual_energy_rate = unit_b['energy_accumulation']['energy_rate']
            actual_momentum_strength = unit_c['temporal_momentum']['momentum_strength']
            
            # Determine actual state (same logic as predictor)
            if actual_energy_rate > 1.6 and actual_momentum_strength > 1.25:
                actual_state = "expanding"
            elif actual_energy_rate > 1.4 and actual_momentum_strength > 1.0:
                actual_state = "pre_cascade"
            else:
                actual_state = "consolidating"
            
            # Calculate accuracy metrics
            prediction = self.predictions[prediction_id]
            
            state_correct = prediction['predicted_state'] == actual_state
            energy_error = abs(prediction['energy_rate_forecast'] - actual_energy_rate) / actual_energy_rate * 100
            momentum_error = abs(prediction['momentum_strength_forecast'] - actual_momentum_strength) / actual_momentum_strength * 100
            
            # Overall accuracy score
            state_score = 1.0 if state_correct else 0.0
            energy_score = max(0, 1.0 - energy_error / 20)  # 20% error = 0 score
            momentum_score = max(0, 1.0 - momentum_error / 20)
            overall_score = (state_score + energy_score + momentum_score) / 3
            
            # Update prediction record
            self.predictions[prediction_id].update({
                'actual_state': actual_state,
                'actual_energy_rate': actual_energy_rate,
                'actual_momentum_strength': actual_momentum_strength,
                'state_prediction_correct': state_correct,
                'energy_rate_error_percent': energy_error,
                'momentum_strength_error_percent': momentum_error,
                'overall_accuracy_score': overall_score
            })
            
            self._save_predictions()
            
            print(f"✅ Prediction {prediction_id} validated")
            print(f"📊 State Prediction: {'✅ CORRECT' if state_correct else '❌ INCORRECT'}")
            print(f"📊 Energy Rate Error: {energy_error:.1f}%")
            print(f"📊 Momentum Error: {momentum_error:.1f}%")
            print(f"📊 Overall Accuracy: {overall_score:.1%}")
            
        except Exception as e:
            print(f"❌ Validation failed: {e}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Generate performance summary of all predictions"""
        
        validated_predictions = [p for p in self.predictions if p.get('overall_accuracy_score') is not None]
        
        if not validated_predictions:
            return {"status": "no_validated_predictions"}
        
        # Calculate summary statistics
        state_accuracy = sum(1 for p in validated_predictions if p['state_prediction_correct']) / len(validated_predictions)
        avg_energy_error = sum(p['energy_rate_error_percent'] for p in validated_predictions) / len(validated_predictions)
        avg_momentum_error = sum(p['momentum_strength_error_percent'] for p in validated_predictions) / len(validated_predictions)
        avg_overall_score = sum(p['overall_accuracy_score'] for p in validated_predictions) / len(validated_predictions)
        
        # Integration score correlation
        avg_integration_score = sum(p['integration_score'] for p in validated_predictions) / len(validated_predictions)
        
        return {
            "total_predictions": len(self.predictions),
            "validated_predictions": len(validated_predictions),
            "state_prediction_accuracy": state_accuracy,
            "average_energy_rate_error_percent": avg_energy_error,
            "average_momentum_strength_error_percent": avg_momentum_error,
            "average_overall_accuracy_score": avg_overall_score,
            "average_integration_score": avg_integration_score,
            "performance_grade": self._grade_performance(avg_overall_score)
        }
    
    def _grade_performance(self, score: float) -> str:
        """Grade the prediction system performance"""
        if score >= 0.9:
            return "EXCELLENT (A+)"
        elif score >= 0.8:
            return "VERY GOOD (A)"
        elif score >= 0.7:
            return "GOOD (B)"
        elif score >= 0.6:
            return "FAIR (C)"
        else:
            return "NEEDS IMPROVEMENT (D)"

def log_ny_am_prediction():
    """Log the NY AM prediction we just generated"""
    
    # Recreate the prediction result structure
    prediction_result = {
        'predicted_state': type('MockState', (), {'value': 'consolidating'})(),
        'state_confidence': 0.425,
        'energy_rate_forecast': 1.642,
        'momentum_strength_forecast': 1.356,
        'price_targets': {
            'target_high': 23375.00,
            'target_low': 23325.00,
            'primary_target': 23350.00
        },
        'timing_windows': {
            'session_open_event': (0.0, 2.0),
            'primary_move': (1.9, 11.9),
            'consolidation_phase': (16.9, 36.9),
            'secondary_move': (36.9, 66.9)
        },
        'hmm_monte_carlo_integration_score': 0.613
    }
    
    validator = PredictionValidator()
    validator.log_prediction(prediction_result, "NY_AM_2025_07_25")
    
    print("🎯 NY AM Friday prediction logged for future validation")
    print("📋 When NY AM session data becomes available, run:")
    print("   validator.validate_prediction(0, 'ny_am_grokEnhanced_2025_07_25.json')")

if __name__ == "__main__":
    log_ny_am_prediction()