#!/usr/bin/env python3
"""
Formula Evolution Framework
Evolves timing prediction formulas using genetic algorithm approach.
Fitness measured by timing accuracy (NOT price accuracy).
"""

import json
import random
import math
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from collections import defaultdict
import copy

@dataclass
class FormulaVariant:
    """Represents a timing formula variant for evolution"""
    formula_id: str
    base_formula: str
    exponent: float
    additional_factors: List[str]
    combination_type: str
    performance_score: float
    timing_accuracy_minutes: float
    generation: int
    parent_ids: List[str]

@dataclass
class TimingAccuracyResult:
    """Results of timing accuracy testing"""
    formula_id: str
    predicted_timing: datetime
    actual_timing: datetime
    timing_error_minutes: float
    success: bool  # True if error <= 5 minutes
    confidence: float
    test_event: str

class FormulaEvolution:
    """Genetic algorithm for evolving timing prediction formulas"""
    
    def __init__(self):
        self.base_formula = "T_memory^1.5 * FVG_density"
        self.variations = []
        self.performance_history = []
        self.generation = 0
        self.population_size = 20
        self.mutation_rate = 0.3
        self.crossover_rate = 0.7
        self.fitness_function = self._timing_accuracy_fitness
        
        # Evolution parameters
        self.formula_variations = {
            "exponents": [1.3, 1.5, 1.7, 2.0, 2.5],
            "additional_factors": [
                "volatility_index", "options_proximity", 
                "session_character_weight", "fvg_cluster_density",
                "liquidity_magnetic_pull", "t_memory_acceleration",
                "consolidation_strength", "expansion_momentum"
            ],
            "combination_types": ["additive", "multiplicative", "exponential", "logarithmic"]
        }
        
        # Fitness criteria
        self.fitness_criteria = {
            "primary": "timing_accuracy_minutes",
            "weights": {
                "timing_error": 0.6,    # Most important
                "confidence": 0.2,      # Secondary
                "consistency": 0.2      # Tertiary
            },
            "success_threshold": 5.0,   # <5 minutes = success
            "failure_threshold": 10.0   # >10 minutes = failure
        }
        
        print("🧬 Formula Evolution Framework initialized")
        print(f"   Population size: {self.population_size}")
        print(f"   Success threshold: {self.fitness_criteria['success_threshold']} minutes")
        print(f"   Base formula: {self.base_formula}")
    
    def initialize_population(self) -> List[FormulaVariant]:
        """Create initial population of formula variants"""
        
        population = []
        
        # Add Grok 4's enhanced formula as first variant
        grok4_variant = FormulaVariant(
            formula_id="grok4_enhanced_001",
            base_formula="Grok 4 Enhanced Formula",
            exponent=1.0,  # Not used for this formula
            additional_factors=["volatility_adjustment", "options_expiry_magnetism"],
            combination_type="enhanced",
            performance_score=0.0,
            timing_accuracy_minutes=float('inf'),
            generation=0,
            parent_ids=[]
        )
        population.append(grok4_variant)
        
        # Generate remaining population with random variants
        for i in range(self.population_size - 1):
            # Random formula parameters
            exponent = random.choice(self.formula_variations["exponents"])
            num_factors = random.randint(1, 3)
            additional_factors = random.sample(
                self.formula_variations["additional_factors"], 
                num_factors
            )
            combination_type = random.choice(self.formula_variations["combination_types"])
            
            # Create formula variant
            variant = FormulaVariant(
                formula_id=f"gen0_formula_{i+1:02d}",
                base_formula=self.base_formula,
                exponent=exponent,
                additional_factors=additional_factors,
                combination_type=combination_type,
                performance_score=0.0,
                timing_accuracy_minutes=float('inf'),
                generation=0,
                parent_ids=[]
            )
            
            population.append(variant)
        
        print(f"🔬 Generated initial population of {len(population)} formula variants")
        print(f"🧠 Including Grok 4's enhanced formula with volatility adjustment and options expiry magnetism")
        return population
    
    def generate_formula_code(self, variant: FormulaVariant) -> str:
        """Generate executable Python code for a formula variant"""
        
        # Check for Grok 4's enhanced formula
        if variant.formula_id.startswith("grok4_enhanced"):
            return self._generate_grok4_enhanced_formula()
        
        base_code = f"result = t_memory**{variant.exponent} * fvg_density"
        
        # Add additional factors based on combination type
        for factor in variant.additional_factors:
            if variant.combination_type == "additive":
                base_code += f" + {factor}"
            elif variant.combination_type == "multiplicative":
                base_code += f" * {factor}"
            elif variant.combination_type == "exponential":
                base_code += f" * math.exp({factor} * 0.1)"
            elif variant.combination_type == "logarithmic":
                base_code += f" * math.log(1 + {factor})"
        
        return base_code
    
    def _generate_grok4_enhanced_formula(self) -> str:
        """Generate Grok 4's enhanced timing formula with volatility adjustment and options expiry magnetism"""
        return """
# Grok 4's Enhanced Formula
time_to_event = 22.5 / max(0.1, gamma_enhanced) * (1 - 0.2 * fvg_proximity) * math.exp(-volatility_index / max(0.1, t_memory))

# Options expiry magnetism (when < 10 minutes to :00 or :30)
minutes_to_half_hour = min(abs(30 - (predicted_time_minutes % 30)), predicted_time_minutes % 30)
if minutes_to_half_hour < 10:
    time_to_event -= (minutes_to_half_hour / 30) * 0.3

result = time_to_event
"""
    
    def evaluate_formula_on_historical_data(self, variant: FormulaVariant) -> List[TimingAccuracyResult]:
        """Test formula variant against historical cascade timing data"""
        
        # Load July 23rd cascade timing validation data
        historical_events = [
            {
                "event": "july23_cascade",
                "actual_time": datetime(2025, 7, 23, 13, 45),
                "t_memory": 15.0,
                "fvg_density": 0.78,
                "volatility_index": 1.2,
                "options_proximity": 0.85,
                "session_character_weight": 0.7,
                "fvg_cluster_density": 0.82,
                "liquidity_magnetic_pull": 0.65,
                "t_memory_acceleration": 2.1,
                "consolidation_strength": 0.4,
                "expansion_momentum": 1.3,
                # Additional variables for Grok 4's enhanced formula
                "gamma_enhanced": 2.5,
                "fvg_proximity": 0.25,
                "predicted_time_minutes": 45  # For options expiry calculation
            }
            # Add more historical events for robust testing
        ]
        
        results = []
        
        for event_data in historical_events:
            try:
                # Prepare variables for formula execution
                t_memory = event_data["t_memory"]
                fvg_density = event_data["fvg_density"]
                volatility_index = event_data["volatility_index"]
                options_proximity = event_data["options_proximity"]
                session_character_weight = event_data["session_character_weight"]
                fvg_cluster_density = event_data["fvg_cluster_density"]
                liquidity_magnetic_pull = event_data["liquidity_magnetic_pull"]
                t_memory_acceleration = event_data["t_memory_acceleration"]
                consolidation_strength = event_data["consolidation_strength"]
                expansion_momentum = event_data["expansion_momentum"]
                # Grok 4 enhanced formula variables
                gamma_enhanced = event_data.get("gamma_enhanced", 2.0)
                fvg_proximity = event_data.get("fvg_proximity", 0.3)
                predicted_time_minutes = event_data.get("predicted_time_minutes", 45)
                
                # Execute formula
                formula_code = self.generate_formula_code(variant)
                local_vars = locals().copy()
                exec(formula_code, {"math": math}, local_vars)
                formula_result = local_vars["result"]
                
                # Convert formula result to timing prediction
                # Normalize result and convert to time offset in minutes
                base_time = datetime(2025, 7, 23, 13, 30)  # Base session start
                timing_offset_minutes = min(60, max(0, formula_result * 2.0))  # Scale and bound
                predicted_time = base_time + timedelta(minutes=timing_offset_minutes)
                
                # Calculate timing accuracy
                actual_time = event_data["actual_time"]
                timing_error = abs((predicted_time - actual_time).total_seconds() / 60)
                success = timing_error <= self.fitness_criteria["success_threshold"]
                
                result = TimingAccuracyResult(
                    formula_id=variant.formula_id,
                    predicted_timing=predicted_time,
                    actual_timing=actual_time,
                    timing_error_minutes=timing_error,
                    success=success,
                    confidence=0.8,  # Base confidence
                    test_event=event_data["event"]
                )
                
                results.append(result)
                
            except Exception as e:
                # Formula execution failed - assign worst possible score
                result = TimingAccuracyResult(
                    formula_id=variant.formula_id,
                    predicted_timing=datetime(2025, 7, 23, 12, 0),
                    actual_timing=event_data["actual_time"],
                    timing_error_minutes=999.0,
                    success=False,
                    confidence=0.0,
                    test_event=event_data["event"]
                )
                results.append(result)
        
        return results
    
    def _timing_accuracy_fitness(self, variant: FormulaVariant, test_results: List[TimingAccuracyResult]) -> float:
        """Calculate fitness score based on timing accuracy (NOT price accuracy)"""
        
        if not test_results:
            return 0.0
        
        # Calculate weighted fitness
        total_fitness = 0.0
        weights = self.fitness_criteria["weights"]
        
        for result in test_results:
            # Timing error component (inverted - lower error = higher fitness)
            timing_fitness = max(0, 1.0 - (result.timing_error_minutes / 60.0))
            
            # Confidence component
            confidence_fitness = result.confidence
            
            # Consistency component (success rate)
            consistency_fitness = 1.0 if result.success else 0.0
            
            # Weighted combination
            combined_fitness = (
                timing_fitness * weights["timing_error"] +
                confidence_fitness * weights["confidence"] +
                consistency_fitness * weights["consistency"]
            )
            
            total_fitness += combined_fitness
        
        # Average fitness across all test results
        average_fitness = total_fitness / len(test_results)
        
        # Update variant performance metrics
        avg_timing_error = np.mean([r.timing_error_minutes for r in test_results])
        variant.performance_score = average_fitness
        variant.timing_accuracy_minutes = avg_timing_error
        
        return average_fitness
    
    def evolve_generation(self, population: List[FormulaVariant]) -> List[FormulaVariant]:
        """Evolve one generation using genetic algorithm"""
        
        print(f"🧬 Evolving generation {self.generation}")
        
        # Evaluate fitness for entire population
        evaluated_population = []
        for variant in population:
            test_results = self.evaluate_formula_on_historical_data(variant)
            fitness = self._timing_accuracy_fitness(variant, test_results)
            evaluated_population.append(variant)
        
        # Sort by fitness (higher is better)
        evaluated_population.sort(key=lambda v: v.performance_score, reverse=True)
        
        # Selection: Keep top 50% of population
        survivors = evaluated_population[:self.population_size // 2]
        
        # Report generation statistics
        best_variant = survivors[0]
        avg_fitness = np.mean([v.performance_score for v in evaluated_population])
        avg_timing_error = np.mean([v.timing_accuracy_minutes for v in evaluated_population])
        
        print(f"   Best fitness: {best_variant.performance_score:.3f}")
        print(f"   Best timing error: {best_variant.timing_accuracy_minutes:.1f} minutes")
        print(f"   Average fitness: {avg_fitness:.3f}")
        print(f"   Average timing error: {avg_timing_error:.1f} minutes")
        
        # Create next generation
        next_generation = survivors.copy()  # Keep survivors
        
        # Generate offspring through crossover and mutation
        while len(next_generation) < self.population_size:
            # Select parents (tournament selection)
            parent1 = self._tournament_selection(survivors)
            parent2 = self._tournament_selection(survivors)
            
            # Crossover
            if random.random() < self.crossover_rate:
                child = self._crossover(parent1, parent2)
            else:
                child = copy.deepcopy(random.choice([parent1, parent2]))
            
            # Mutation
            if random.random() < self.mutation_rate:
                child = self._mutate(child)
            
            # Update child metadata
            child.formula_id = f"gen{self.generation+1}_formula_{len(next_generation)+1:02d}"
            child.generation = self.generation + 1
            child.parent_ids = [parent1.formula_id, parent2.formula_id]
            child.performance_score = 0.0
            child.timing_accuracy_minutes = float('inf')
            
            next_generation.append(child)
        
        return next_generation
    
    def _tournament_selection(self, population: List[FormulaVariant], tournament_size: int = 3) -> FormulaVariant:
        """Select parent using tournament selection"""
        tournament = random.sample(population, min(tournament_size, len(population)))
        return max(tournament, key=lambda v: v.performance_score)
    
    def _crossover(self, parent1: FormulaVariant, parent2: FormulaVariant) -> FormulaVariant:
        """Create offspring through crossover"""
        child = FormulaVariant(
            formula_id="",  # Will be set later
            base_formula=parent1.base_formula,
            exponent=random.choice([parent1.exponent, parent2.exponent]),
            additional_factors=list(set(parent1.additional_factors + parent2.additional_factors))[:3],
            combination_type=random.choice([parent1.combination_type, parent2.combination_type]),
            performance_score=0.0,
            timing_accuracy_minutes=float('inf'),
            generation=0,  # Will be set later
            parent_ids=[]  # Will be set later
        )
        return child
    
    def _mutate(self, variant: FormulaVariant) -> FormulaVariant:
        """Apply mutation to a variant"""
        mutated = copy.deepcopy(variant)
        
        # Mutate exponent
        if random.random() < 0.3:
            mutated.exponent = random.choice(self.formula_variations["exponents"])
        
        # Mutate additional factors
        if random.random() < 0.3:
            num_factors = random.randint(1, 3)
            mutated.additional_factors = random.sample(
                self.formula_variations["additional_factors"], 
                num_factors
            )
        
        # Mutate combination type
        if random.random() < 0.3:
            mutated.combination_type = random.choice(self.formula_variations["combination_types"])
        
        return mutated
    
    def run_evolution(self, num_generations: int = 10) -> Dict:
        """Run the complete evolution process"""
        
        print(f"🚀 Starting formula evolution for {num_generations} generations")
        
        # Initialize population
        population = self.initialize_population()
        
        # Evolution loop
        best_variants_per_generation = []
        
        for gen in range(num_generations):
            self.generation = gen
            
            # Evolve generation
            population = self.evolve_generation(population)
            
            # Track best variant
            best_variant = max(population, key=lambda v: v.performance_score)
            best_variants_per_generation.append(copy.deepcopy(best_variant))
            
            # Early stopping if we achieve excellent timing accuracy
            if best_variant.timing_accuracy_minutes <= 2.0:
                print(f"🎯 Excellent timing accuracy achieved: {best_variant.timing_accuracy_minutes:.1f} minutes")
                break
        
        # Final results
        final_best = best_variants_per_generation[-1]
        
        results = {
            "evolution_metadata": {
                "generations_run": self.generation + 1,
                "population_size": self.population_size,
                "success_threshold": self.fitness_criteria["success_threshold"],
                "base_formula": self.base_formula,
                "evolution_completed": datetime.now().isoformat()
            },
            "best_formula": {
                "formula_id": final_best.formula_id,
                "formula_code": self.generate_formula_code(final_best),
                "exponent": final_best.exponent,
                "additional_factors": final_best.additional_factors,
                "combination_type": final_best.combination_type,
                "performance_score": final_best.performance_score,
                "timing_accuracy_minutes": final_best.timing_accuracy_minutes,
                "generation": final_best.generation
            },
            "evolution_progress": [
                {
                    "generation": i,
                    "best_fitness": variant.performance_score,
                    "best_timing_accuracy": variant.timing_accuracy_minutes,
                    "formula_id": variant.formula_id
                }
                for i, variant in enumerate(best_variants_per_generation)
            ],
            "final_population": [
                {
                    "formula_id": v.formula_id,
                    "performance_score": v.performance_score,
                    "timing_accuracy_minutes": v.timing_accuracy_minutes,
                    "formula_code": self.generate_formula_code(v)
                }
                for v in sorted(population, key=lambda x: x.performance_score, reverse=True)[:5]
            ]
        }
        
        print(f"\n🏆 EVOLUTION COMPLETE")
        print(f"   Best formula: {final_best.formula_id}")
        print(f"   Final fitness: {final_best.performance_score:.3f}")
        print(f"   Final timing accuracy: {final_best.timing_accuracy_minutes:.1f} minutes")
        print(f"   Success: {'✅' if final_best.timing_accuracy_minutes <= 5.0 else '⚠️'}")
        
        return results
    
    def save_evolution_results(self, results: Dict, filename: str = None):
        """Save evolution results to file"""
        if filename is None:
            filename = f"formula_evolution_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"💾 Evolution results saved to: {filename}")

def main():
    """Demonstrate formula evolution framework"""
    
    print("🧬 FORMULA EVOLUTION FRAMEWORK - Timing Accuracy Optimization")
    print("=" * 65)
    
    # Initialize evolution framework
    evolution = FormulaEvolution()
    
    # Run evolution
    results = evolution.run_evolution(num_generations=5)
    
    # Save results
    evolution.save_evolution_results(results)
    
    # Display final results
    best_formula = results["best_formula"]
    print(f"\n📊 FINAL EVOLVED FORMULA:")
    print(f"   Formula Code: {best_formula['formula_code']}")
    print(f"   Timing Accuracy: {best_formula['timing_accuracy_minutes']:.1f} minutes")
    print(f"   Performance Score: {best_formula['performance_score']:.3f}")
    print(f"   Fitness Criteria: Timing accuracy (NOT price accuracy)")
    
    if best_formula['timing_accuracy_minutes'] <= 5.0:
        print(f"   🎯 SUCCESS: Formula achieves <5min timing accuracy threshold!")
    else:
        print(f"   ⚠️ Needs improvement: Formula exceeds 5min timing threshold")

if __name__ == "__main__":
    main()