"""
Monte Carlo Simulation with Full Tracker Integration
Uses completed Asia session + 3 updated tracker files to predict next session outcomes
"""

import json
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import logging
from monte_carlo import PathGenerator, Event, EventType, PathResult

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
class TrackerEnhancedMonteCarlo:
    """Monte Carlo simulation enhanced with full tracker context"""
    
    def __init__(self, asia_session_file: str, htf_context_file: str, 
                 fvg_state_file: str, liquidity_state_file: str):
        """
        Initialize with Asia session results and all 3 tracker files
        
        Args:
            asia_session_file: Completed Asia session with Grok calculations
            htf_context_file: Updated HTF context post-Asia
            fvg_state_file: Updated FVG state post-Asia  
            liquidity_state_file: Updated liquidity registry post-Asia
        """
        # Load all data files
        with open(asia_session_file, 'r') as f:
            self.asia_session = json.load(f)
            
        with open(htf_context_file, 'r') as f:
            self.htf_context = json.load(f)
            
        with open(fvg_state_file, 'r') as f:
            self.fvg_state = json.load(f)
            
        with open(liquidity_state_file, 'r') as f:
            self.liquidity_state = json.load(f)
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def extract_enhanced_parameters(self, target_session: str = "midnight") -> Tuple[Dict, Dict]:
        """
        Extract Monte Carlo parameters from Asia results + tracker context
        
        Args:
            target_session: Target session to simulate ("midnight" or "london")
            
        Returns:
            Tuple of (tracker_state, session_params)
        """
        # Extract T_memory from FVG carryover
        fpfvg_metadata = self.fvg_state.get("fpfvg_carryover_metadata", {})
        t_memory_base = fpfvg_metadata.get("t_memory_final", 5.0)
        
        # Extract from Asia Grok calculations for enhanced parameters
        grok_calcs = self.asia_session.get("grok_enhanced_calculations", {})
        unit_a = grok_calcs.get("unit_a_foundation", {})
        
        # Get enhanced parameters from Unit A tracker enhancements
        tracker_enhancements = unit_a.get("tracker_enhancements", {})
        t_memory_applied = tracker_enhancements.get("t_memory_applied", t_memory_base)
        liquidity_gradient_adj = tracker_enhancements.get("liquidity_gradient_adjustment", 0.003)
        
        # Get gamma from foundation calculations
        foundation_calcs = unit_a.get("foundation_calculations", {})
        gamma_enhanced = foundation_calcs.get("gamma_base", 1.471)
        
        # Extract E threshold from Unit B energy calculations
        unit_b = grok_calcs.get("unit_b_energy_structure", {})
        energy_calcs = unit_b.get("energy_calculations", {})
        e_threshold_adj = energy_calcs.get("E_threshold", 1000)
        
        # Extract liquidity levels from liquidity registry
        untaken_liquidity = []
        liquidity_registry = self.liquidity_state.get("untaken_liquidity_registry", [])
        for entry in liquidity_registry:
            level = entry.get("level", 0)
            weight = entry.get("weight", 1.0)
            # Include levels with significant weight
            if weight >= 1.5:
                untaken_liquidity.append(level)
        
        # Extract HTF structures from HTF context
        htf_structures = []
        active_structures = self.htf_context.get("active_structures", [])
        for structure in active_structures:
            level = structure.get("level", 0)
            strength = structure.get("strength", 1.0)
            # Include structures with meaningful strength
            if strength >= 1.0:
                htf_structures.append(level)
        
        # Get current price from Asia session end
        original_session = self.asia_session.get("original_session_data", {})
        price_data = original_session.get("price_data", {})
        current_price = price_data.get("close", 23329.0)
        
        # Session start price for target session (assume small gap)
        gap_factor = np.random.normal(1.0, 0.002)  # Small random gap
        session_start_price = current_price * gap_factor
        
        tracker_state = {
            't_memory': t_memory_applied,
            'untaken_liquidity': sorted(untaken_liquidity),
            'liquidity_gradient': liquidity_gradient_adj,
            'htf_structures': sorted(htf_structures),
            'e_threshold_adj': e_threshold_adj
        }
        
        session_params = {
            'gamma_enhanced': gamma_enhanced,
            'current_price': session_start_price,
            'session_start_price': session_start_price
        }
        
        return tracker_state, session_params
    
    def run_session_prediction(self, target_session: str, n_simulations: int = 1000, 
                              duration_minutes: int = 300) -> Dict:
        """
        Run Monte Carlo prediction for target session
        
        Args:
            target_session: "midnight" or "london"
            n_simulations: Number of Monte Carlo paths
            duration_minutes: Session duration
            
        Returns:
            Complete prediction results with comparison metadata
        """
        self.logger.info(f"Running {n_simulations} Monte Carlo simulations for {target_session} session")
        self.logger.info(f"Using Asia session + tracker context from July 22nd")
        
        # Extract enhanced parameters
        tracker_state, session_params = self.extract_enhanced_parameters(target_session)
        
        # Create path generator with enhanced context
        generator = PathGenerator(tracker_state, session_params)
        
        # Run simulations
        paths = generator.generate_paths(n_simulations, duration_minutes)
        
        # Calculate prediction bands
        prediction_bands = generator.calculate_prediction_bands(paths)
        event_analysis = generator.analyze_event_frequencies(paths)
        
        # Prepare results with metadata
        results = {
            "monte_carlo_predictions": {
                "target_session": target_session,
                "prediction_timestamp": "2025-07-22T00:00:00-04:00",  # NY Time
                "prediction_bands": prediction_bands,
                "event_analysis": event_analysis,
                "simulation_metadata": {
                    "n_simulations": n_simulations,
                    "duration_minutes": duration_minutes,
                    "total_paths": len(paths)
                }
            },
            "input_context": {
                "asia_session_source": "asia_grokEnhanced_2025_07_22.json",
                "htf_context_source": "HTF_Context_Asia_grokEnhanced_2025_07_22.json",
                "fvg_state_source": "FVG_State_Asia_grokEnhanced_2025_07_22.json", 
                "liquidity_state_source": "Liquidity_State_Asia_grokEnhanced_2025_07_22.json"
            },
            "extracted_parameters": {
                "tracker_state": tracker_state,
                "session_params": session_params
            },
            "parameter_sources": {
                "t_memory": f"From Asia Unit A tracker_enhancements: {tracker_state['t_memory']}",
                "gamma_enhanced": f"From Asia Unit A foundation_calculations: {session_params['gamma_enhanced']}",
                "liquidity_gradient": f"From Asia Unit A liquidity adjustments: {tracker_state['liquidity_gradient']}",
                "untaken_liquidity": f"From liquidity registry: {len(tracker_state['untaken_liquidity'])} levels",
                "htf_structures": f"From HTF context: {len(tracker_state['htf_structures'])} structures"
            }
        }
        
        return results
    
    def compare_with_actual(self, predictions: Dict, actual_session_file: str) -> Dict:
        """
        Compare Monte Carlo predictions with actual session results
        
        Args:
            predictions: Monte Carlo prediction results
            actual_session_file: Path to actual session results
            
        Returns:
            Comparison analysis
        """
        with open(actual_session_file, 'r') as f:
            actual_session = json.load(f)
        
        # Extract actual session data
        actual_price_data = actual_session.get("price_data", {})
        actual_final_price = actual_price_data.get("close", 0)
        actual_range = actual_price_data.get("range", 0)
        actual_session_character = actual_price_data.get("session_character", "unknown")
        
        # Get prediction bands
        pred_bands = predictions["monte_carlo_predictions"]["prediction_bands"]
        final_price_percentiles = pred_bands["final_price_percentiles"]
        range_percentiles = pred_bands["range_percentiles"]
        
        # Determine which percentile the actual result fell into
        def find_percentile_bracket(actual_value, percentiles):
            if actual_value <= percentiles["10th"]:
                return "Below 10th percentile"
            elif actual_value <= percentiles["25th"]:
                return "10th-25th percentile"
            elif actual_value <= percentiles["50th"]:
                return "25th-50th percentile"
            elif actual_value <= percentiles["75th"]:
                return "50th-75th percentile"
            elif actual_value <= percentiles["90th"]:
                return "75th-90th percentile"
            else:
                return "Above 90th percentile"
        
        price_percentile = find_percentile_bracket(actual_final_price, final_price_percentiles)
        range_percentile = find_percentile_bracket(actual_range, range_percentiles)
        
        comparison = {
            "prediction_accuracy": {
                "actual_final_price": actual_final_price,
                "predicted_50th_percentile": final_price_percentiles["50th"],
                "price_prediction_error": abs(actual_final_price - final_price_percentiles["50th"]),
                "actual_price_percentile_bracket": price_percentile,
                
                "actual_range": actual_range,
                "predicted_50th_percentile_range": range_percentiles["50th"],
                "range_prediction_error": abs(actual_range - range_percentiles["50th"]),
                "actual_range_percentile_bracket": range_percentile
            },
            "session_character_analysis": {
                "actual_session_character": actual_session_character,
                "predicted_dominant_events": predictions["monte_carlo_predictions"]["event_analysis"]["frequencies"]
            },
            "overall_assessment": {
                "price_prediction_quality": "Good" if "25th-75th" in price_percentile else "Needs Improvement",
                "range_prediction_quality": "Good" if "25th-75th" in range_percentile else "Needs Improvement"
            }
        }
        
        return comparison

def run_midnight_london_predictions():
    """Run predictions for both Midnight and London sessions"""
    
    # Initialize tracker-enhanced Monte Carlo
    simulator = TrackerEnhancedMonteCarlo(
        asia_session_file="asia_grokEnhanced_2025_07_22.json",
        htf_context_file="HTF_Context_Asia_grokEnhanced_2025_07_22.json",
        fvg_state_file="FVG_State_Asia_grokEnhanced_2025_07_22.json",
        liquidity_state_file="Liquidity_State_Asia_grokEnhanced_2025_07_22.json"
    )
    
    # Predict Midnight session
    print("🕛 Predicting MIDNIGHT session...")
    midnight_predictions = simulator.run_session_prediction("midnight", n_simulations=1000, duration_minutes=300)
    
    # Save midnight predictions
    with open("midnight_monte_carlo_predictions_2025_07_22.json", "w") as f:
        json.dump(midnight_predictions, f, indent=2)
    
    print(f"Midnight Predictions:")
    bands = midnight_predictions["monte_carlo_predictions"]["prediction_bands"]
    print(f"  50th percentile final price: {bands['final_price_percentiles']['50th']:.2f}")
    print(f"  90th percentile range: {bands['range_percentiles']['90th']:.2f}")
    
    # Compare with actual midnight results
    try:
        midnight_comparison = simulator.compare_with_actual(
            midnight_predictions, "midnight_grokEnhanced_2025_07_22.json")
        
        with open("midnight_prediction_vs_actual_2025_07_22.json", "w") as f:
            json.dump(midnight_comparison, f, indent=2)
            
        print("\n📊 Midnight Prediction vs Actual:")
        acc = midnight_comparison["prediction_accuracy"]
        print(f"  Actual final price: {acc['actual_final_price']}")
        print(f"  Predicted 50th percentile: {acc['predicted_50th_percentile']:.2f}")
        print(f"  Prediction error: {acc['price_prediction_error']:.2f}")
        print(f"  Actual fell in: {acc['actual_price_percentile_bracket']}")
        
    except FileNotFoundError:
        print("  ⚠️  Midnight actual results not found for comparison")
    
    # Predict London session (using same Asia context)
    print("\n🇬🇧 Predicting LONDON session...")
    london_predictions = simulator.run_session_prediction("london", n_simulations=1000, duration_minutes=300)
    
    # Save london predictions  
    with open("london_monte_carlo_predictions_2025_07_22.json", "w") as f:
        json.dump(london_predictions, f, indent=2)
    
    print(f"London Predictions:")
    bands = london_predictions["monte_carlo_predictions"]["prediction_bands"]
    print(f"  50th percentile final price: {bands['final_price_percentiles']['50th']:.2f}")
    print(f"  90th percentile range: {bands['range_percentiles']['90th']:.2f}")
    
    # Compare with actual london results
    try:
        london_comparison = simulator.compare_with_actual(
            london_predictions, "london_grokEnhanced_2025_07_22.json")
        
        with open("london_prediction_vs_actual_2025_07_22.json", "w") as f:
            json.dump(london_comparison, f, indent=2)
            
        print("\n📊 London Prediction vs Actual:")
        acc = london_comparison["prediction_accuracy"]
        print(f"  Actual final price: {acc['actual_final_price']}")
        print(f"  Predicted 50th percentile: {acc['predicted_50th_percentile']:.2f}")
        print(f"  Prediction error: {acc['price_prediction_error']:.2f}")
        print(f"  Actual fell in: {acc['actual_price_percentile_bracket']}")
        
    except FileNotFoundError:
        print("  ⚠️  London actual results not found for comparison")

if __name__ == "__main__":
    run_midnight_london_predictions()