#!/usr/bin/env python3
"""
Event Timing Logger
Tracks timing accuracy metrics instead of price accuracy.
Replaces price-based validation with event timing validation.
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from collections import defaultdict
import numpy as np

@dataclass
class TimingPrediction:
    event_type: str
    predicted_time: datetime
    predicted_window_start: datetime 
    predicted_window_end: datetime
    confidence: float
    methodology: str
    prediction_timestamp: datetime

@dataclass
class TimingActual:
    event_type: str
    actual_time: datetime
    detection_timestamp: datetime
    context: str

@dataclass
class TimingAccuracy:
    prediction_id: str
    event_type: str
    predicted_time: datetime
    actual_time: datetime
    timing_error_minutes: float
    within_window: bool
    within_5min_threshold: bool
    success: bool
    confidence: float
    methodology: str

class EventTimingLogger:
    """Logs and tracks event timing accuracy instead of price accuracy"""
    
    def __init__(self, log_file: str = "event_timing_accuracy.json"):
        self.log_file = log_file
        self.predictions = []
        self.actuals = []
        self.accuracy_records = []
        self.load_existing_logs()
    
    def load_existing_logs(self):
        """Load existing timing logs if they exist"""
        try:
            if os.path.exists(self.log_file):
                with open(self.log_file, 'r') as f:
                    data = json.load(f)
                
                # Convert back to dataclasses
                for pred_data in data.get('predictions', []):
                    pred_data['predicted_time'] = datetime.fromisoformat(pred_data['predicted_time'])
                    pred_data['predicted_window_start'] = datetime.fromisoformat(pred_data['predicted_window_start'])
                    pred_data['predicted_window_end'] = datetime.fromisoformat(pred_data['predicted_window_end'])
                    pred_data['prediction_timestamp'] = datetime.fromisoformat(pred_data['prediction_timestamp'])
                    self.predictions.append(TimingPrediction(**pred_data))
                
                for actual_data in data.get('actuals', []):
                    actual_data['actual_time'] = datetime.fromisoformat(actual_data['actual_time'])
                    actual_data['detection_timestamp'] = datetime.fromisoformat(actual_data['detection_timestamp'])
                    self.actuals.append(TimingActual(**actual_data))
                
                for acc_data in data.get('accuracy_records', []):
                    acc_data['predicted_time'] = datetime.fromisoformat(acc_data['predicted_time'])
                    acc_data['actual_time'] = datetime.fromisoformat(acc_data['actual_time'])
                    self.accuracy_records.append(TimingAccuracy(**acc_data))
                
                print(f"📊 Loaded {len(self.predictions)} predictions, {len(self.actuals)} actuals, {len(self.accuracy_records)} accuracy records")
        
        except Exception as e:
            print(f"⚠️ Could not load existing logs: {e}")
    
    def log_prediction(self, event_type: str, predicted_time: datetime, 
                      window_start: datetime, window_end: datetime,
                      confidence: float, methodology: str) -> str:
        """Log a new timing prediction"""
        
        prediction_id = f"{event_type}_{predicted_time.strftime('%Y%m%d_%H%M%S')}_{len(self.predictions)}"
        
        prediction = TimingPrediction(
            event_type=event_type,
            predicted_time=predicted_time,
            predicted_window_start=window_start,
            predicted_window_end=window_end,
            confidence=confidence,
            methodology=methodology,
            prediction_timestamp=datetime.now()
        )
        
        self.predictions.append(prediction)
        self.save_logs()
        
        print(f"📝 Logged prediction: {event_type} at {predicted_time.strftime('%H:%M')} (window: {window_start.strftime('%H:%M')}-{window_end.strftime('%H:%M')})")
        
        return prediction_id
    
    def log_actual_event(self, event_type: str, actual_time: datetime, context: str = ""):
        """Log an actual event occurrence"""
        
        actual = TimingActual(
            event_type=event_type,
            actual_time=actual_time,
            detection_timestamp=datetime.now(),
            context=context
        )
        
        self.actuals.append(actual)
        
        # Try to match with recent predictions
        self.match_and_validate_timing(actual)
        
        self.save_logs()
        
        print(f"✅ Logged actual event: {event_type} at {actual_time.strftime('%H:%M')} ({context})")
    
    def match_and_validate_timing(self, actual: TimingActual):
        """Match actual event with predictions and calculate timing accuracy"""
        
        # Find matching predictions (same event type within reasonable time)
        matching_predictions = [
            p for p in self.predictions 
            if (p.event_type == actual.event_type and 
                abs((p.predicted_time - actual.actual_time).total_seconds()) < 3600)  # Within 1 hour
        ]
        
        for prediction in matching_predictions:
            # Check if we already validated this prediction
            existing_validations = [
                acc for acc in self.accuracy_records
                if (acc.predicted_time == prediction.predicted_time and 
                    acc.event_type == prediction.event_type)
            ]
            
            if existing_validations:
                continue  # Already validated
            
            # Calculate timing accuracy
            timing_error_minutes = abs((actual.actual_time - prediction.predicted_time).total_seconds() / 60)
            within_window = prediction.predicted_window_start <= actual.actual_time <= prediction.predicted_window_end
            within_5min = timing_error_minutes <= 5.0
            success = within_window or within_5min
            
            # Create accuracy record
            prediction_id = f"{prediction.event_type}_{prediction.predicted_time.strftime('%Y%m%d_%H%M%S')}"
            
            accuracy = TimingAccuracy(
                prediction_id=prediction_id,
                event_type=prediction.event_type,
                predicted_time=prediction.predicted_time,
                actual_time=actual.actual_time,
                timing_error_minutes=timing_error_minutes,
                within_window=within_window,
                within_5min_threshold=within_5min,
                success=success,
                confidence=prediction.confidence,
                methodology=prediction.methodology
            )
            
            self.accuracy_records.append(accuracy)
            
            status = "✅ SUCCESS" if success else "❌ MISS"
            print(f"   {status} Timing validation: {timing_error_minutes:.1f}min error, window: {within_window}, <5min: {within_5min}")
    
    def calculate_timing_accuracy_metrics(self) -> Dict:
        """Calculate comprehensive timing accuracy metrics"""
        
        if not self.accuracy_records:
            return {"error": "No accuracy records available"}
        
        # Overall metrics
        total_predictions = len(self.accuracy_records)
        successful_predictions = sum(1 for acc in self.accuracy_records if acc.success)
        success_rate = successful_predictions / total_predictions if total_predictions > 0 else 0
        
        # Timing error metrics
        timing_errors = [acc.timing_error_minutes for acc in self.accuracy_records]
        mean_timing_error = np.mean(timing_errors)
        median_timing_error = np.median(timing_errors)
        
        # Window accuracy
        within_window_count = sum(1 for acc in self.accuracy_records if acc.within_window)
        window_accuracy_rate = within_window_count / total_predictions if total_predictions > 0 else 0
        
        # 5-minute threshold accuracy
        within_5min_count = sum(1 for acc in self.accuracy_records if acc.within_5min_threshold)
        threshold_accuracy_rate = within_5min_count / total_predictions if total_predictions > 0 else 0
        
        # By event type
        event_type_metrics = defaultdict(list)
        for acc in self.accuracy_records:
            event_type_metrics[acc.event_type].append(acc)
        
        event_metrics = {}
        for event_type, records in event_type_metrics.items():
            successful = sum(1 for r in records if r.success)
            event_metrics[event_type] = {
                "total_predictions": len(records),
                "successful_predictions": successful,
                "success_rate": successful / len(records) if records else 0,
                "mean_timing_error_minutes": np.mean([r.timing_error_minutes for r in records])
            }
        
        # By methodology
        methodology_metrics = defaultdict(list)
        for acc in self.accuracy_records:
            methodology_metrics[acc.methodology].append(acc)
        
        method_metrics = {}
        for methodology, records in methodology_metrics.items():
            successful = sum(1 for r in records if r.success)
            method_metrics[methodology] = {
                "total_predictions": len(records),
                "successful_predictions": successful,
                "success_rate": successful / len(records) if records else 0,
                "mean_timing_error_minutes": np.mean([r.timing_error_minutes for r in records])
            }
        
        return {
            "overall_metrics": {
                "total_predictions": total_predictions,
                "successful_predictions": successful_predictions,
                "success_rate": success_rate,
                "mean_timing_error_minutes": mean_timing_error,
                "median_timing_error_minutes": median_timing_error,
                "window_accuracy_rate": window_accuracy_rate,
                "threshold_5min_accuracy_rate": threshold_accuracy_rate
            },
            "event_type_breakdown": event_metrics,
            "methodology_breakdown": method_metrics,
            "recent_performance": self.get_recent_performance(),
            "timing_quality_assessment": self.assess_timing_quality(success_rate, mean_timing_error)
        }
    
    def get_recent_performance(self, days: int = 7) -> Dict:
        """Get timing accuracy for recent predictions"""
        
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_records = [
            acc for acc in self.accuracy_records 
            if acc.predicted_time >= cutoff_date
        ]
        
        if not recent_records:
            return {"no_recent_data": f"No predictions in last {days} days"}
        
        successful = sum(1 for acc in recent_records if acc.success)
        total = len(recent_records)
        
        return {
            "period_days": days,
            "total_predictions": total,
            "successful_predictions": successful,
            "success_rate": successful / total if total > 0 else 0,
            "mean_timing_error_minutes": np.mean([acc.timing_error_minutes for acc in recent_records])
        }
    
    def assess_timing_quality(self, success_rate: float, mean_error: float) -> str:
        """Assess overall timing prediction quality"""
        
        if success_rate >= 0.8 and mean_error <= 3.0:
            return "Excellent - High accuracy with precise timing"
        elif success_rate >= 0.7 and mean_error <= 5.0:
            return "Good - Reliable timing predictions"
        elif success_rate >= 0.5 and mean_error <= 8.0:
            return "Moderate - Acceptable timing accuracy"
        elif success_rate >= 0.3:
            return "Poor - Needs timing model improvement"
        else:
            return "Very Poor - Timing predictions unreliable"
    
    def export_timing_report(self, output_file: str = None) -> str:
        """Export comprehensive timing accuracy report"""
        
        if output_file is None:
            output_file = f"timing_accuracy_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        metrics = self.calculate_timing_accuracy_metrics()
        
        report = {
            "report_metadata": {
                "generated_at": datetime.now().isoformat(),
                "total_predictions_logged": len(self.predictions),
                "total_actual_events_logged": len(self.actuals),
                "total_accuracy_records": len(self.accuracy_records),
                "paradigm": "Event timing prediction (WHEN not WHERE)"
            },
            "timing_accuracy_metrics": metrics,
            "detailed_accuracy_records": [
                {
                    "prediction_id": acc.prediction_id,
                    "event_type": acc.event_type,
                    "predicted_time": acc.predicted_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "actual_time": acc.actual_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "timing_error_minutes": acc.timing_error_minutes,
                    "within_window": acc.within_window,
                    "within_5min_threshold": acc.within_5min_threshold,
                    "success": acc.success,
                    "confidence": acc.confidence,
                    "methodology": acc.methodology
                }
                for acc in self.accuracy_records
            ]
        }
        
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"📊 Timing accuracy report exported to {output_file}")
        return output_file
    
    def save_logs(self):
        """Save all logs to file"""
        
        data = {
            "metadata": {
                "paradigm": "Event timing prediction validation",
                "last_updated": datetime.now().isoformat(),
                "total_predictions": len(self.predictions),
                "total_actuals": len(self.actuals),
                "total_validations": len(self.accuracy_records)
            },
            "predictions": [asdict(pred) for pred in self.predictions],
            "actuals": [asdict(actual) for actual in self.actuals],
            "accuracy_records": [asdict(acc) for acc in self.accuracy_records]
        }
        
        # Convert datetime objects to ISO strings for JSON serialization
        def convert_datetimes(obj):
            if isinstance(obj, dict):
                return {k: convert_datetimes(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_datetimes(item) for item in obj]
            elif isinstance(obj, datetime):
                return obj.isoformat()
            else:
                return obj
        
        data = convert_datetimes(data)
        
        with open(self.log_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def log_july23_validation_results(self):
        """Log the successful July 23rd cascade timing validation"""
        
        # Log the prediction from cross-session predictor
        prediction_time = datetime(2025, 7, 23, 13, 45)
        window_start = datetime(2025, 7, 23, 13, 42)
        window_end = datetime(2025, 7, 23, 13, 48)
        
        pred_id = self.log_prediction(
            event_type="cascade_initiation",
            predicted_time=prediction_time,
            window_start=window_start,
            window_end=window_end,
            confidence=0.78,
            methodology="cross_session_event_predictor"
        )
        
        # Log the actual cascade that occurred
        actual_time = datetime(2025, 7, 23, 13, 45)  # Exact match!
        
        self.log_actual_event(
            event_type="cascade_initiation",
            actual_time=actual_time,
            context="lunch_session_high_sweep_cascade"
        )
        
        print("✅ July 23rd validation results logged - Perfect timing match!")

def main():
    """Demonstrate event timing logger"""
    
    print("📊 EVENT TIMING LOGGER - Timing Accuracy Tracking")
    print("=" * 55)
    
    logger = EventTimingLogger()
    
    # Log July 23rd validation success
    print("\n🎯 Logging July 23rd cascade timing validation...")
    logger.log_july23_validation_results()
    
    # Calculate and display metrics
    print("\n📈 TIMING ACCURACY METRICS:")
    metrics = logger.calculate_timing_accuracy_metrics()
    
    if "error" not in metrics:
        overall = metrics["overall_metrics"]
        print(f"   Success Rate: {overall['success_rate']:.1%}")
        print(f"   Mean Timing Error: {overall['mean_timing_error_minutes']:.1f} minutes")
        print(f"   Window Accuracy: {overall['window_accuracy_rate']:.1%}")
        print(f"   ±5min Threshold: {overall['threshold_5min_accuracy_rate']:.1%}")
        print(f"   Quality Assessment: {metrics['timing_quality_assessment']}")
        
        # Show event type breakdown
        if metrics["event_type_breakdown"]:
            print(f"\n📊 BY EVENT TYPE:")
            for event_type, stats in metrics["event_type_breakdown"].items():
                print(f"   {event_type}: {stats['success_rate']:.1%} success, {stats['mean_timing_error_minutes']:.1f}min avg error")
    
    # Export report
    print(f"\n💾 Exporting timing accuracy report...")
    report_file = logger.export_timing_report()
    
    print(f"\n🏆 PARADIGM VALIDATION:")
    print(f"   FROM: Price prediction accuracy (WHERE price goes)")
    print(f"   TO: Event timing accuracy (WHEN events occur)")
    print(f"   Status: ✅ Successfully tracking event timing accuracy")
    
    return logger

if __name__ == "__main__":
    main()