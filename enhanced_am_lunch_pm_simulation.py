#!/usr/bin/env python3
"""
Enhanced AM + Lunch → PM Simulation
Uses combined AM momentum and lunch tracker states with event-sequence intelligence
to predict PM session outcomes for July 23rd.
"""

import json
import os
from datetime import datetime

from src.experimental.cross_session_predictor import CrossSessionPredictionSystem, CrossSessionState
from monte_carlo_adapter import MonteCarloAdapter

def load_session_data(session_name: str, date: str) -> tuple:
    """Load session data and tracker files"""
    session_file = f"{session_name}_grokEnhanced_{date}.json"
    htf_file = f"HTF_Context_{session_name.upper()}_grokEnhanced_{date}.json"
    fvg_file = f"FVG_State_{session_name.upper()}_grokEnhanced_{date}.json"
    liquidity_file = f"Liquidity_State_{session_name.upper()}_grokEnhanced_{date}.json"
    
    with open(session_file, 'r') as f:
        session_data = json.load(f)
    with open(htf_file, 'r') as f:
        htf_tracker = json.load(f)
    with open(fvg_file, 'r') as f:
        fvg_tracker = json.load(f)
    with open(liquidity_file, 'r') as f:
        liquidity_tracker = json.load(f)
    
    return session_data, htf_tracker, fvg_tracker, liquidity_tracker

def merge_am_lunch_context(am_data: tuple, lunch_data: tuple) -> dict:
    """
    Merge AM momentum with lunch tracker states for enhanced PM prediction
    
    Args:
        am_data: (session_data, htf_tracker, fvg_tracker, liquidity_tracker)
        lunch_data: (session_data, htf_tracker, fvg_tracker, liquidity_tracker)
        
    Returns:
        Combined context for PM prediction
    """
    am_session, am_htf, am_fvg, am_liquidity = am_data
    lunch_session, lunch_htf, lunch_fvg, lunch_liquidity = lunch_data
    
    # Extract AM momentum characteristics
    am_state = CrossSessionState(am_session)
    am_state.update_from_tracker_data(am_htf, am_fvg, am_liquidity)
    
    # Extract lunch ending state
    lunch_state = CrossSessionState(lunch_session)
    lunch_state.update_from_tracker_data(lunch_htf, lunch_fvg, lunch_liquidity)
    
    # Calculate combined momentum with AM influence and lunch carryover
    time_gap_am_to_lunch = 1.5  # 1.5 hours from AM close to lunch close
    am_momentum_transfer = am_state.transfer_to_next_session(time_gap_am_to_lunch)
    
    time_gap_lunch_to_pm = 0.5  # 0.5 hours from lunch close to PM open
    lunch_momentum_transfer = lunch_state.transfer_to_next_session(time_gap_lunch_to_pm)
    
    # Merge momentum with lunch state priority (more recent)
    combined_momentum = {
        'primary_momentum': lunch_momentum_transfer['carried_momentum'],
        'am_influence': {
            'strength': am_momentum_transfer['carried_momentum']['strength'] * 0.3,  # Decay AM influence
            'directional_bias': am_momentum_transfer['carried_momentum']['directional_bias'],
            'character_evolution': am_momentum_transfer['evolved_character']
        },
        'lunch_dominance': lunch_momentum_transfer['carried_momentum'],
        'combined_energy': (am_momentum_transfer['carried_energy'] * 0.3 + 
                          lunch_momentum_transfer['carried_energy'] * 0.7),
        'session_sequence_position': 6  # 6th session in chain
    }
    
    # Use latest tracker states (lunch end)
    combined_context = {
        'momentum_analysis': combined_momentum,
        'tracker_states': {
            'htf_context': lunch_htf,
            'fvg_state': lunch_fvg,
            'liquidity_state': lunch_liquidity,
            't_memory': lunch_fvg.get('t_memory', 5.0),
            'session_continuity': 'unprecedented_6_session_chain'
        },
        'session_evolution': {
            'am_character': am_session.get('price_data', {}).get('session_character', 'unknown'),
            'lunch_character': lunch_session.get('price_data', {}).get('session_character', 'unknown'),
            'evolution_pattern': f"{am_state.session_character}→{lunch_state.session_character}",
        },
        'combined_session_data': lunch_session,  # Use lunch as base session
        'enhancement_metadata': {
            'merger_timestamp': datetime.now().isoformat(),
            'am_momentum_decay': am_momentum_transfer['transfer_metadata']['momentum_decay_applied'],
            'lunch_momentum_decay': lunch_momentum_transfer['transfer_metadata']['momentum_decay_applied'],
            'combined_approach': 'am_momentum_plus_lunch_tracker_states'
        }
    }
    
    return combined_context

def run_enhanced_pm_simulation(date: str = "2025_07_23") -> dict:
    """Run enhanced AM+Lunch→PM simulation with event-sequence intelligence"""
    
    print("🚀 ENHANCED AM + LUNCH → PM SIMULATION")
    print("=" * 50)
    print(f"Date: {date}")
    print(f"Method: Combined AM momentum + Lunch tracker states")
    print(f"Intelligence: Event-sequence analysis + Monte Carlo")
    print()
    
    try:
        # Step 1: Load AM and Lunch session data
        print("1️⃣ Loading AM and Lunch session data...")
        am_data = load_session_data("nyam", date)
        lunch_data = load_session_data("lunch", date)
        print(f"   ✅ AM session loaded: {am_data[0].get('session_metadata', {}).get('session_id', 'nyam')}")
        print(f"   ✅ Lunch session loaded: {lunch_data[0].get('session_metadata', {}).get('session_id', 'lunch')}")
        
        # Step 2: Merge AM momentum with lunch tracker states
        print("\n2️⃣ Merging AM momentum with lunch tracker states...")
        combined_context = merge_am_lunch_context(am_data, lunch_data)
        
        momentum = combined_context['momentum_analysis']
        print(f"   📊 Combined Momentum Strength: {momentum['combined_energy']:.4f}")
        print(f"   📊 AM Influence: {momentum['am_influence']['strength']:.4f}")
        print(f"   📊 Lunch Dominance: {momentum['lunch_dominance']['strength']:.4f}")
        print(f"   📊 T_memory: {combined_context['tracker_states']['t_memory']:.2f}")
        print(f"   📊 Session Chain Position: {momentum['session_sequence_position']}")
        
        # Step 3: Run cross-session prediction with enhanced context
        print("\n3️⃣ Running enhanced cross-session prediction...")
        system = CrossSessionPredictionSystem(error_threshold=25.0)
        
        # Use lunch session as base with enhanced momentum
        enhanced_session_data = combined_context['combined_session_data'].copy()
        
        # Inject combined momentum into session metadata
        enhanced_session_data['enhanced_momentum_context'] = combined_context['momentum_analysis']
        enhanced_session_data['tracker_enhancement'] = combined_context['tracker_states']
        
        # Run prediction using lunch→PM but with enhanced context
        prediction_results = system.predictor.predict_london_from_asia(
            enhanced_session_data,
            (combined_context['tracker_states']['htf_context'],
             combined_context['tracker_states']['fvg_state'],
             combined_context['tracker_states']['liquidity_state'])
        )
        
        print(f"   🎯 Enhanced PM Close Prediction: {prediction_results.predicted_close:.2f}")
        print(f"   🎯 Enhanced PM Range: {prediction_results.predicted_range[0]:.2f} - {prediction_results.predicted_range[1]:.2f}")
        print(f"   🎯 Session Character: {prediction_results.session_character_prediction}")
        print(f"   🎯 Confidence (Enhanced): {prediction_results.confidence_level:.2f}")
        
        # Check event-sequence intelligence
        metadata = prediction_results.prediction_metadata
        if 'asia_event_analysis' in metadata and 'london_event_predictions' in metadata:
            asia_events = metadata['asia_event_analysis']['total_events']
            event_confidence = metadata['london_event_predictions']['event_tree_confidence']
            print(f"   🔗 Event Intelligence: {asia_events} events, confidence {event_confidence:.2f}")
        
        # Step 4: Run Monte Carlo simulation on combined context
        print("\n4️⃣ Running Monte Carlo simulation with enhanced parameters...")
        adapter = MonteCarloAdapter(enhanced_session_data)
        monte_carlo_results = adapter.create_path_generator()
        
        # Generate paths for PM session (3 hours = 180 minutes)
        paths = monte_carlo_results.generate_paths(n_simulations=1000, duration_minutes=180)
        prediction_bands = monte_carlo_results.calculate_prediction_bands(paths)
        
        print(f"   📈 Monte Carlo 50th percentile: {prediction_bands['final_price_percentiles']['50th']:.2f}")
        print(f"   📈 Monte Carlo 75th percentile: {prediction_bands['final_price_percentiles']['75th']:.2f}")
        print(f"   📈 Monte Carlo 90th percentile range: {prediction_bands['range_percentiles']['90th']:.2f}")
        
        # Step 5: Compile enhanced results
        print("\n5️⃣ Compiling enhanced simulation results...")
        
        enhanced_results = {
            'enhanced_simulation_metadata': {
                'simulation_type': 'am_lunch_to_pm_enhanced',
                'date': date,
                'method': 'combined_momentum_and_tracker_states',
                'intelligence_layers': ['event_sequence_analysis', 'monte_carlo_simulation'],
                'session_chain_position': 6,
                'timestamp': datetime.now().isoformat()
            },
            'input_data_summary': {
                'am_session_character': combined_context['session_evolution']['am_character'],
                'lunch_session_character': combined_context['session_evolution']['lunch_character'],
                'evolution_pattern': combined_context['session_evolution']['evolution_pattern'],
                'combined_momentum_strength': momentum['combined_energy'],
                't_memory_carryover': combined_context['tracker_states']['t_memory']
            },
            'cross_session_prediction': {
                'predicted_close': prediction_results.predicted_close,
                'predicted_range': prediction_results.predicted_range,
                'predicted_high': prediction_results.predicted_high,
                'predicted_low': prediction_results.predicted_low,
                'session_character_prediction': prediction_results.session_character_prediction,
                'confidence_level': prediction_results.confidence_level,
                'momentum_decay_timeline': prediction_results.momentum_decay_timeline,
                'liquidity_interaction_forecast': prediction_results.liquidity_interaction_forecast,
                'prediction_metadata': prediction_results.prediction_metadata
            },
            'monte_carlo_analysis': {
                'prediction_bands': prediction_bands,
                'simulation_metadata': {
                    'n_simulations': 1000,
                    'duration_minutes': 180,
                    'total_paths': len(paths)
                }
            },
            'combined_context_analysis': combined_context
        }
        
        # Step 6: Price validation
        print("\n6️⃣ Validating price outputs...")
        all_prices = [
            prediction_results.predicted_close,
            prediction_results.predicted_high,
            prediction_results.predicted_low,
            prediction_bands['final_price_percentiles']['50th'],
            prediction_bands['final_price_percentiles']['75th']
        ]
        
        all_positive = all(price > 0 for price in all_prices)
        print(f"   🎯 Price Validation: {'✅ ALL POSITIVE' if all_positive else '❌ NEGATIVE DETECTED'}")
        
        if all_positive:
            print(f"   ✅ Cross-session prediction: {prediction_results.predicted_close:.2f}")
            print(f"   ✅ Monte Carlo median: {prediction_bands['final_price_percentiles']['50th']:.2f}")
            print(f"   ✅ Range validation: {prediction_results.predicted_high - prediction_results.predicted_low:.2f} points")
        
        return enhanced_results
        
    except Exception as e:
        print(f"❌ Enhanced simulation failed: {str(e)}")
        return {
            'error': str(e),
            'simulation_type': 'am_lunch_to_pm_enhanced',
            'status': 'failed'
        }

def main():
    """Main execution"""
    results = run_enhanced_pm_simulation("2025_07_23")
    
    # Save results
    output_file = "enhanced_am_lunch_pm_simulation_2025_07_23.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Enhanced simulation results saved to: {output_file}")
    
    if 'error' not in results:
        print(f"\n🎯 ENHANCED SIMULATION SUMMARY")
        print("=" * 35)
        
        cross_pred = results['cross_session_prediction']
        monte_carlo = results['monte_carlo_analysis']
        
        print(f"Cross-Session Prediction: {cross_pred['predicted_close']:.2f}")
        print(f"Monte Carlo Median: {monte_carlo['prediction_bands']['final_price_percentiles']['50th']:.2f}")
        print(f"Combined Confidence: {cross_pred['confidence_level']:.2f}")
        print(f"Session Character: {cross_pred['session_character_prediction']}")
        
        input_summary = results['input_data_summary']
        print(f"Evolution: {input_summary['evolution_pattern']}")
        print(f"T_memory: {input_summary['t_memory_carryover']:.2f}")
        print(f"Combined Momentum: {input_summary['combined_momentum_strength']:.4f}")
        
        print(f"\n🚀 Enhanced AM+Lunch→PM simulation completed successfully!")
    else:
        print(f"\n❌ Simulation failed - debug data saved")

if __name__ == "__main__":
    main()