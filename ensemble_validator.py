#!/usr/bin/env python3
"""
Ensemble Validation System
Comprehensive testing and validation framework for the adaptive ensemble system
"""

import json
import numpy as np
import sys
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

sys.path.append('.')
from src.utils import load_json_data, save_json_data

try:
    from adaptive_ensemble_predictor import AdaptivePredictor
    from performance_tracker import PerformanceTracker
    from corrected_grok_predictor import CorrectedGrokPredictor
except ImportError as e:
    print(f"⚠️ Import warning: {e}")

@dataclass
class ValidationResult:
    """Result from ensemble validation"""
    test_name: str
    ensemble_error_pct: float
    best_component_error_pct: float
    worst_component_error_pct: float
    ensemble_vs_best_component: float  # How much better/worse than best component
    active_predictors: Dict[str, float]
    strategy_used: str
    prevented_misapplication: bool
    quality_grade: str

class EnsembleValidator:
    """Comprehensive validation system for ensemble predictions"""
    
    def __init__(self):
        self.test_cases = []
        self.validation_results = []
    
    def validate_consolidation_prevention(self) -> ValidationResult:
        """Test that consolidation scaling isn't applied during expansion sessions"""
        
        print("🧪 CONSOLIDATION MISAPPLICATION PREVENTION TEST")
        print("=" * 55)
        
        # Load July 23rd data (expansion session)
        try:
            lunch_data = load_json_data('lunch_grokEnhanced_2025_07_23.json')
            htf_tracker = load_json_data('HTF_Context_Lunch_grokEnhanced_2025_07_23.json')
            fvg_tracker = load_json_data('FVG_State_Lunch_grokEnhanced_2025_07_23.json')
            liquidity_tracker = load_json_data('Liquidity_State_Lunch_grokEnhanced_2025_07_23.json')
            actual_pm_data = load_json_data('ny_pm_grokEnhanced_2025_07_23.json')
        except FileNotFoundError as e:
            print(f"❌ Test data not found: {e}")
            return None
        
        # Test ensemble prediction
        predictor = AdaptivePredictor()
        tracker_context = (htf_tracker, fvg_tracker, liquidity_tracker)
        
        ensemble_result = predictor.predict(lunch_data, tracker_context, actual_pm_data)
        
        # Analyze results
        actual_close = actual_pm_data['original_session_data']['price_data']['close']
        actual_range = actual_pm_data['original_session_data']['price_data']['range']
        
        ensemble_error = abs(ensemble_result.predicted_close - actual_close)
        ensemble_error_pct = (ensemble_error / actual_range) * 100
        
        # Check component errors
        component_errors = {}
        for name, prediction in ensemble_result.component_predictions.items():
            error = abs(prediction - actual_close)
            error_pct = (error / actual_range) * 100
            component_errors[name] = error_pct
        
        best_component_error = min(component_errors.values())
        worst_component_error = max(component_errors.values())
        
        # Check if consolidation scaling was prevented
        consolidation_weight = ensemble_result.active_predictors.get('consolidation_scaled', 0.0)
        expansion_weight = ensemble_result.active_predictors.get('expansion_enhanced', 0.0)
        
        prevented_misapplication = (expansion_weight > consolidation_weight) and (expansion_weight > 0.5)
        
        print(f"📊 PREVENTION TEST RESULTS:")
        print(f"   Session Type: expansion_consolidation_final_expansion")
        print(f"   Expansion Weight: {expansion_weight:.2f}")
        print(f"   Consolidation Weight: {consolidation_weight:.2f}")
        print(f"   Misapplication Prevented: {'✅ YES' if prevented_misapplication else '❌ NO'}")
        print(f"   Ensemble Error: {ensemble_error_pct:.1f}%")
        print(f"   Best Component Error: {best_component_error:.1f}%")
        
        return ValidationResult(
            test_name="consolidation_prevention",
            ensemble_error_pct=ensemble_error_pct,
            best_component_error_pct=best_component_error,
            worst_component_error_pct=worst_component_error,
            ensemble_vs_best_component=ensemble_error_pct - best_component_error,
            active_predictors=ensemble_result.active_predictors,
            strategy_used=ensemble_result.ensemble_strategy,
            prevented_misapplication=prevented_misapplication,
            quality_grade=self._assess_quality(ensemble_error_pct)
        )
    
    def validate_performance_learning(self) -> ValidationResult:
        """Test that performance tracking improves predictions over time"""
        
        print("\n🧪 PERFORMANCE LEARNING VALIDATION TEST")
        print("=" * 45)
        
        # Create predictor with fresh performance tracker
        predictor = AdaptivePredictor()
        
        # Simulate multiple predictions to build history
        test_scenarios = [
            # Scenario 1: expansion_enhanced performs well
            {
                'predictor': 'expansion_enhanced',
                'predicted': 23348.0,
                'actual': 23350.0,
                'range': 162.5,
                'character': 'expansion_consolidation_final_expansion'
            },
            # Scenario 2: base_monte_carlo performs poorly  
            {
                'predictor': 'base_monte_carlo',
                'predicted': 23150.0,
                'actual': 23350.0,
                'range': 162.5,
                'character': 'expansion_consolidation_final_expansion'
            },
            # Scenario 3: consolidation_scaled inappropriate for expansion
            {
                'predictor': 'consolidation_scaled',
                'predicted': 23260.0,
                'actual': 23350.0,
                'range': 162.5,
                'character': 'expansion_consolidation_final_expansion'
            }
        ]
        
        print("📝 Building performance history:")
        for scenario in test_scenarios:
            predictor.performance_tracker.record_prediction(
                predictor_name=scenario['predictor'],
                predicted_value=scenario['predicted'],
                actual_value=scenario['actual'],
                session_range=scenario['range'],
                session_character=scenario['character']
            )
            error_pct = (abs(scenario['predicted'] - scenario['actual']) / scenario['range']) * 100
            print(f"   {scenario['predictor']}: {error_pct:.1f}% error")
        
        # Now get adaptive weights
        adaptive_weights = predictor.performance_tracker.get_adaptive_weights(
            ['expansion_enhanced', 'base_monte_carlo', 'consolidation_scaled'],
            'expansion_consolidation_final_expansion'
        )
        
        print(f"\n📊 ADAPTIVE WEIGHT RESULTS:")
        for name, weight in adaptive_weights.items():
            print(f"   {name}: {weight:.2f}")
        
        # Test if expansion_enhanced gets highest weight
        expansion_weight = adaptive_weights.get('expansion_enhanced', 0.0)
        best_predictor = max(adaptive_weights, key=adaptive_weights.get)
        learning_successful = (best_predictor == 'expansion_enhanced') and (expansion_weight > 0.5)
        
        print(f"   Best Predictor: {best_predictor}")
        print(f"   Learning Successful: {'✅ YES' if learning_successful else '❌ NO'}")
        
        return ValidationResult(
            test_name="performance_learning",
            ensemble_error_pct=0.0,  # Not applicable for this test
            best_component_error_pct=1.1,  # expansion_enhanced error
            worst_component_error_pct=123.1,  # base_monte_carlo error
            ensemble_vs_best_component=0.0,
            active_predictors=adaptive_weights,
            strategy_used="performance_based_weighting",
            prevented_misapplication=learning_successful,
            quality_grade="excellent" if learning_successful else "poor"
        )
    
    def validate_context_detection(self) -> ValidationResult:
        """Test that context detection correctly identifies session characteristics"""
        
        print("\n🧪 CONTEXT DETECTION VALIDATION TEST")
        print("=" * 40)
        
        # Load actual PM data
        try:
            with open('ny_pm_grokEnhanced_2025_07_23.json', 'r') as f:
                actual_pm_data = json.load(f)
        except FileNotFoundError as e:
            print(f"❌ Test data not found: {e}")
            return None
        
        predictor = AdaptivePredictor()
        session_data = actual_pm_data['original_session_data']
        
        # Test context detection
        context = predictor.context_detector.detect_market_context(session_data)
        
        # Expected results for July 23rd session
        expected_character = 'expansion_consolidation_final_expansion'
        expected_primary_phase = 'expansion'
        expected_complexity = 0.9  # High complexity for mixed session
        
        character_correct = context['session_character'] == expected_character
        phase_correct = context['primary_phase'] == expected_primary_phase
        complexity_correct = abs(context['complexity_score'] - expected_complexity) < 0.2
        
        print(f"📊 CONTEXT DETECTION RESULTS:")
        print(f"   Expected Character: {expected_character}")
        print(f"   Detected Character: {context['session_character']}")
        print(f"   Character Correct: {'✅ YES' if character_correct else '❌ NO'}")
        print(f"   Expected Phase: {expected_primary_phase}")
        print(f"   Detected Phase: {context['primary_phase']}")
        print(f"   Phase Correct: {'✅ YES' if phase_correct else '❌ NO'}")
        print(f"   Complexity Score: {context['complexity_score']:.1f}")
        print(f"   Complexity Correct: {'✅ YES' if complexity_correct else '❌ NO'}")
        
        all_correct = character_correct and phase_correct and complexity_correct
        
        return ValidationResult(
            test_name="context_detection",
            ensemble_error_pct=0.0,  # Not applicable
            best_component_error_pct=0.0,
            worst_component_error_pct=0.0,
            ensemble_vs_best_component=0.0,
            active_predictors={},
            strategy_used="context_detection",
            prevented_misapplication=all_correct,
            quality_grade="excellent" if all_correct else "poor"
        )
    
    def run_comprehensive_validation(self) -> Dict[str, ValidationResult]:
        """Run all validation tests"""
        
        print("🚀 COMPREHENSIVE ENSEMBLE VALIDATION")
        print("=" * 40)
        
        results = {}
        
        # Test 1: Consolidation misapplication prevention
        result1 = self.validate_consolidation_prevention()
        if result1:
            results['consolidation_prevention'] = result1
        
        # Test 2: Performance learning
        result2 = self.validate_performance_learning()
        if result2:
            results['performance_learning'] = result2
        
        # Test 3: Context detection
        result3 = self.validate_context_detection()
        if result3:
            results['context_detection'] = result3
        
        return results
    
    def generate_validation_report(self, results: Dict[str, ValidationResult]) -> str:
        """Generate comprehensive validation report"""
        
        report = [
            "📋 ENSEMBLE VALIDATION REPORT",
            "=" * 35,
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        ]
        
        overall_score = 0
        total_tests = len(results)
        
        for test_name, result in results.items():
            report.extend([
                f"\n🧪 {test_name.upper().replace('_', ' ')}:",
                f"   Quality: {result.quality_grade.title()}",
                f"   Prevention Success: {'✅' if result.prevented_misapplication else '❌'}",
                f"   Strategy: {result.strategy_used}"
            ])
            
            if result.ensemble_error_pct > 0:
                report.extend([
                    f"   Ensemble Error: {result.ensemble_error_pct:.1f}%",
                    f"   Best Component: {result.best_component_error_pct:.1f}%",
                    f"   Vs Best Component: {result.ensemble_vs_best_component:+.1f}%"
                ])
            
            # Score this test
            if result.quality_grade == "excellent":
                overall_score += 1.0
            elif result.quality_grade == "good":
                overall_score += 0.7
            elif result.quality_grade == "moderate":
                overall_score += 0.5
            else:
                overall_score += 0.0
        
        # Overall assessment
        overall_pct = (overall_score / total_tests) * 100 if total_tests > 0 else 0
        
        report.extend([
            f"\n🏆 OVERALL ASSESSMENT:",
            f"   Score: {overall_score:.1f}/{total_tests} ({overall_pct:.0f}%)",
            f"   Grade: {self._assess_overall_grade(overall_pct)}"
        ])
        
        # Key achievements
        achievements = []
        if results.get('consolidation_prevention', {}).prevented_misapplication:
            achievements.append("✅ Prevented consolidation→expansion misapplication")
        if results.get('performance_learning', {}).prevented_misapplication:
            achievements.append("✅ Performance-based weight adaptation working")
        if results.get('context_detection', {}).prevented_misapplication:
            achievements.append("✅ Accurate context detection")
        
        if achievements:
            report.extend([
                f"\n🎯 KEY ACHIEVEMENTS:",
                *[f"   {achievement}" for achievement in achievements]
            ])
        
        return "\n".join(report)
    
    def _assess_quality(self, error_pct: float) -> str:
        """Assess quality based on error percentage"""
        if error_pct < 10:
            return 'excellent'
        elif error_pct < 20:
            return 'good'
        elif error_pct < 40:
            return 'moderate'
        else:
            return 'poor'
    
    def _assess_overall_grade(self, score_pct: float) -> str:
        """Assess overall grade based on score percentage"""
        if score_pct >= 90:
            return 'A (Excellent)'
        elif score_pct >= 80:
            return 'B (Good)'
        elif score_pct >= 70:
            return 'C (Satisfactory)'
        elif score_pct >= 60:
            return 'D (Needs Improvement)'
        else:
            return 'F (Failing)'

def main():
    """Run comprehensive validation tests"""
    
    validator = EnsembleValidator()
    results = validator.run_comprehensive_validation()
    
    # Generate and display report
    report = validator.generate_validation_report(results)
    print(f"\n{report}")
    
    # Save detailed results
    output_file = f"ensemble_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    serializable_results = {}
    for test_name, result in results.items():
        serializable_results[test_name] = {
            'test_name': result.test_name,
            'ensemble_error_pct': result.ensemble_error_pct,
            'best_component_error_pct': result.best_component_error_pct,
            'worst_component_error_pct': result.worst_component_error_pct,
            'ensemble_vs_best_component': result.ensemble_vs_best_component,
            'active_predictors': result.active_predictors,
            'strategy_used': result.strategy_used,
            'prevented_misapplication': result.prevented_misapplication,
            'quality_grade': result.quality_grade
        }
    
    validation_data = {
        'validation_metadata': {
            'validation_type': 'comprehensive_ensemble_validation',
            'timestamp': datetime.now().isoformat(),
            'total_tests': len(results)
        },
        'test_results': serializable_results,
        'validation_report': report
    }
    
    with open(output_file, 'w') as f:
        json.dump(validation_data, f, indent=2, default=str)
    
    print(f"\n💾 Detailed results saved to: {output_file}")

if __name__ == "__main__":
    main()