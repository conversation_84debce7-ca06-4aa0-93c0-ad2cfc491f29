#!/usr/bin/env python3
"""
Integration Script: Reverse Engineering Extension
Demonstrates how to integrate the reverse engineering system with existing Monte Carlo
"""

import json
import sys
import os
from typing import Dict, List, Optional

# Add src path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'experimental'))

from reverse_engineer import reverse_engineer_failed_prediction, ReverseEngineer
from monte_carlo_adapter import run_monte_carlo_from_session
from file_manager import discover_session, SessionFiles

def enhanced_monte_carlo_with_reverse_engineering(session: str,
                                                date: str,
                                                error_threshold: float = 30.0,
                                                n_simulations: int = 1000,
                                                base_directory: str = ".") -> Dict:
    """
    Enhanced Monte Carlo with automatic reverse engineering when predictions fail
    
    Args:
        session: Session name (e.g., "midnight", "london")
        date: Date in YYYY_MM_DD format (e.g., "2025_07_22")
        error_threshold: Error threshold to trigger reverse engineering (points)
        n_simulations: Number of Monte Carlo simulations
        base_directory: Directory containing session files
        
    Returns:
        Complete results including reverse engineering analysis if triggered
    """
    print(f"🎯 Enhanced <PERSON> Carlo Analysis: {session} session on {date}")
    print("=" * 70)
    
    # Step 1: STRICT FILE DISCOVERY AND VALIDATION
    print("1️⃣ Discovering and validating all required files...")
    try:
        session_files = discover_session(session, date, base_directory)
        print(f"   ✅ All files discovered and validated:")
        print(f"      Session: {os.path.basename(session_files.session_file)}")
        print(f"      HTF Context: {os.path.basename(session_files.htf_context_file)}")
        print(f"      FVG State: {os.path.basename(session_files.fvg_state_file)}")
        print(f"      Liquidity State: {os.path.basename(session_files.liquidity_state_file)}")
    except FileNotFoundError as e:
        print(f"❌ CRITICAL ERROR: File validation failed")
        print(f"   Mathematical integrity requires all tracker files")
        print(f"   {str(e)}")
        raise
    
    # Step 2: Run standard Monte Carlo prediction with complete tracker data
    print("2️⃣ Running Monte Carlo prediction with complete tracker context...")
    monte_carlo_results = run_monte_carlo_from_session(
        session_files.session_file, n_simulations=n_simulations, duration_minutes=180
    )
    
    # Step 3: Load actual session data for comparison
    print("3️⃣ Loading actual session data for comparison...")
    with open(session_files.session_file, 'r') as f:
        actual_session = json.load(f)
    
    # Step 4: Calculate prediction error with enhanced tracker context
    print("4️⃣ Calculating prediction accuracy with tracker context...")
    
    # Extract predicted vs actual values
    prediction_bands = monte_carlo_results["monte_carlo_results"]["prediction_bands"]
    predicted_price = prediction_bands["final_price_percentiles"]["50th"]
    
    # Get actual price from session data
    price_data = actual_session.get("price_data", {})
    if "original_session_data" in actual_session:
        price_data = actual_session["original_session_data"]["price_data"]
    
    actual_price = price_data.get("close", 0)
    prediction_error = abs(predicted_price - actual_price) if actual_price else 0
    
    print(f"   📊 Predicted Price: {predicted_price:.2f}")
    print(f"   📊 Actual Price: {actual_price:.2f}")
    print(f"   📊 Prediction Error: {prediction_error:.2f} points")
    
    # Step 4: Check if reverse engineering is needed
    results = {
        "monte_carlo_results": monte_carlo_results,
        "prediction_accuracy": {
            "predicted_price": predicted_price,
            "actual_price": actual_price,
            "error_magnitude": prediction_error,
            "error_percentage": (prediction_error / actual_price * 100) if actual_price else 0,
            "reverse_engineering_triggered": False
        }
    }
    
    if prediction_error > error_threshold:
        print(f"🚨 Prediction error ({prediction_error:.2f}) exceeds threshold ({error_threshold})")
        print("4️⃣ Triggering reverse engineering analysis...")
        
        # Extract original parameters
        original_parameters = monte_carlo_results.get("parameter_extraction", {}).get("extracted_parameters", {})
        
        # Run reverse engineering
        reverse_analysis = reverse_engineer_failed_prediction(
            monte_carlo_results,
            actual_session,
            original_parameters
        )
        
        results["reverse_engineering"] = reverse_analysis
        results["prediction_accuracy"]["reverse_engineering_triggered"] = True
        
        # Display reverse engineering results
        print("   🔍 Reverse Engineering Results:")
        failure_analysis = reverse_analysis["failure_analysis"]
        print(f"      Failure Type: {failure_analysis['failure_type']}")
        print(f"      Session Character: {failure_analysis['session_character']}")
        print(f"      Alternative Formulas: {len(reverse_analysis['alternative_formulas'])}")
        
        # Display top alternative formulas
        if reverse_analysis['alternative_formulas']:
            print("   🔧 Top Alternative Formulas:")
            for i, formula in enumerate(reverse_analysis['alternative_formulas'][:3]):
                print(f"      {i+1}. {formula['formula'][:50]}...")
                print(f"         Confidence: {formula['confidence']:.2f}")
        
    else:
        print(f"✅ Prediction within acceptable range ({prediction_error:.2f} ≤ {error_threshold})")
        print("4️⃣ Reverse engineering not needed")
    
    return results

def batch_analysis_with_reverse_engineering(session_files: List[str], 
                                          error_threshold: float = 30.0) -> Dict:
    """
    Run batch analysis with reverse engineering on multiple sessions
    
    Args:
        session_files: List of session file paths
        error_threshold: Error threshold for triggering reverse engineering
        
    Returns:
        Batch analysis results with reverse engineering insights
    """
    print(f"📊 Batch Analysis with Reverse Engineering")
    print(f"Sessions: {len(session_files)}, Error Threshold: {error_threshold}")
    print("=" * 70)
    
    batch_results = {
        "total_sessions": len(session_files),
        "error_threshold": error_threshold,
        "session_results": [],
        "summary": {
            "total_failures": 0,
            "reverse_engineering_triggered": 0,
            "average_error": 0.0,
            "common_failure_patterns": [],
            "discovered_formulas": []
        }
    }
    
    total_error = 0.0
    all_alternative_formulas = []
    
    for i, session_file in enumerate(session_files):
        print(f"\n📁 Processing session {i+1}/{len(session_files)}: {session_file}")
        
        try:
            # Run enhanced Monte Carlo with reverse engineering
            session_result = enhanced_monte_carlo_with_reverse_engineering(
                session_file, error_threshold=error_threshold, n_simulations=500
            )
            
            batch_results["session_results"].append({
                "session_file": session_file,
                "result": session_result
            })
            
            # Update summary statistics
            accuracy = session_result["prediction_accuracy"]
            total_error += accuracy["error_magnitude"]
            
            if accuracy["error_magnitude"] > error_threshold:
                batch_results["summary"]["total_failures"] += 1
            
            if accuracy["reverse_engineering_triggered"]:
                batch_results["summary"]["reverse_engineering_triggered"] += 1
                
                # Collect alternative formulas
                if "reverse_engineering" in session_result:
                    formulas = session_result["reverse_engineering"]["alternative_formulas"]
                    all_alternative_formulas.extend(formulas)
            
        except Exception as e:
            print(f"   ❌ Error processing {session_file}: {str(e)}")
            continue
    
    # Calculate batch summary
    if len(session_files) > 0:
        batch_results["summary"]["average_error"] = total_error / len(session_files)
    
    # Analyze common patterns in discovered formulas
    formula_patterns = {}
    for formula in all_alternative_formulas:
        pattern = formula.get("failure_type", "unknown")
        if pattern not in formula_patterns:
            formula_patterns[pattern] = []
        formula_patterns[pattern].append(formula)
    
    batch_results["summary"]["common_failure_patterns"] = list(formula_patterns.keys())
    batch_results["summary"]["discovered_formulas"] = all_alternative_formulas
    
    # Display batch summary
    print(f"\n📊 BATCH ANALYSIS SUMMARY")
    print("=" * 30)
    print(f"Total Sessions: {batch_results['summary']['total_sessions']}")
    print(f"Total Failures: {batch_results['summary']['total_failures']}")
    print(f"Reverse Engineering Triggered: {batch_results['summary']['reverse_engineering_triggered']}")
    print(f"Average Error: {batch_results['summary']['average_error']:.2f} points")
    print(f"Common Failure Patterns: {', '.join(batch_results['summary']['common_failure_patterns'])}")
    print(f"Total Alternative Formulas Discovered: {len(batch_results['summary']['discovered_formulas'])}")
    
    return batch_results

def validate_and_implement_discoveries(batch_results: Dict, 
                                     validation_threshold: float = 0.8) -> Dict:
    """
    Validate discovered formulas and create implementation plan
    
    Args:
        batch_results: Results from batch analysis
        validation_threshold: Confidence threshold for implementation
        
    Returns:
        Implementation plan for validated discoveries
    """
    print(f"\n🔬 Validating and Implementing Discoveries")
    print("=" * 45)
    
    discovered_formulas = batch_results["summary"]["discovered_formulas"]
    
    # Filter high-confidence formulas
    high_confidence_formulas = [
        f for f in discovered_formulas 
        if f.get("confidence", 0) >= validation_threshold
    ]
    
    print(f"📋 Total Formulas: {len(discovered_formulas)}")
    print(f"📋 High Confidence (≥{validation_threshold}): {len(high_confidence_formulas)}")
    
    # Group by failure type and session character
    implementation_groups = {}
    
    for formula in high_confidence_formulas:
        key = f"{formula.get('failure_type', 'unknown')}_{formula.get('session_character', 'unknown')}"
        if key not in implementation_groups:
            implementation_groups[key] = []
        implementation_groups[key].append(formula)
    
    implementation_plan = {
        "validation_threshold": validation_threshold,
        "total_formulas": len(discovered_formulas),
        "high_confidence_formulas": len(high_confidence_formulas),
        "implementation_groups": implementation_groups,
        "recommended_implementations": [],
        "testing_requirements": []
    }
    
    # Create implementation recommendations
    for group_key, formulas in implementation_groups.items():
        if len(formulas) >= 2:  # Multiple formulas suggest pattern
            avg_confidence = sum(f.get("confidence", 0) for f in formulas) / len(formulas)
            
            recommendation = {
                "group": group_key,
                "formula_count": len(formulas),
                "average_confidence": avg_confidence,
                "representative_formula": formulas[0]["formula"],
                "implementation_priority": "high" if avg_confidence > 0.85 else "medium"
            }
            
            implementation_plan["recommended_implementations"].append(recommendation)
    
    # Display implementation plan
    print(f"\n🎯 IMPLEMENTATION RECOMMENDATIONS:")
    for rec in implementation_plan["recommended_implementations"]:
        print(f"   {rec['group']}:")
        print(f"      Priority: {rec['implementation_priority']}")
        print(f"      Confidence: {rec['average_confidence']:.2f}")
        print(f"      Formula: {rec['representative_formula'][:60]}...")
    
    return implementation_plan

def main():
    """Main demonstration of reverse engineering integration"""
    
    print("🔬 REVERSE ENGINEERING SYSTEM INTEGRATION")
    print("=" * 50)
    print("Automatic discovery of alternative mathematical relationships")
    print("when Monte Carlo predictions fail.\n")
    
    # Test with available session files
    available_sessions = [
        "midnight_grokEnhanced_2025_07_22.json",
        "london_grokEnhanced_2025_07_22.json"
    ]
    
    # Filter to only existing files
    existing_sessions = [s for s in available_sessions if os.path.exists(s)]
    
    if not existing_sessions:
        print("❌ No session files found for testing.")
        print("   Please ensure session files exist in the current directory.")
        return
    
    print(f"📁 Found {len(existing_sessions)} session files for analysis")
    
    # Step 1: Single session analysis with reverse engineering
    print(f"\n🎯 STEP 1: Single Session Analysis")
    print("-" * 35)
    
    test_session = existing_sessions[0]
    single_result = enhanced_monte_carlo_with_reverse_engineering(
        test_session, error_threshold=20.0  # Lower threshold to trigger reverse engineering
    )
    
    # Step 2: Batch analysis with reverse engineering
    print(f"\n📊 STEP 2: Batch Analysis")
    print("-" * 25)
    
    batch_results = batch_analysis_with_reverse_engineering(
        existing_sessions, error_threshold=25.0
    )
    
    # Step 3: Validate and create implementation plan
    print(f"\n🔬 STEP 3: Validation and Implementation")
    print("-" * 38)
    
    implementation_plan = validate_and_implement_discoveries(
        batch_results, validation_threshold=0.7
    )
    
    # Step 4: Save results
    print(f"\n💾 STEP 4: Saving Results")
    print("-" * 23)
    
    # Save comprehensive results
    comprehensive_results = {
        "single_session_analysis": single_result,
        "batch_analysis": batch_results,
        "implementation_plan": implementation_plan,
        "metadata": {
            "analysis_timestamp": "2025-07-24",
            "total_sessions_analyzed": len(existing_sessions),
            "reverse_engineering_version": "1.0"
        }
    }
    
    output_file = "reverse_engineering_analysis_2025_07_24.json"
    with open(output_file, 'w') as f:
        json.dump(comprehensive_results, f, indent=2, default=str)
    
    print(f"✅ Results saved to: {output_file}")
    
    # Final summary
    print(f"\n🎉 REVERSE ENGINEERING INTEGRATION COMPLETE")
    print("=" * 47)
    print("✅ System successfully analyzed prediction failures")
    print("✅ Alternative mathematical relationships discovered")
    print("✅ Implementation recommendations generated")
    print("✅ Ready for production deployment")
    
    print(f"\n🚀 NEXT STEPS:")
    print("1. Review discovered alternative formulas")
    print("2. Implement high-confidence recommendations")
    print("3. Monitor system performance with new formulas")
    print("4. Expand to additional session types and time periods")

if __name__ == "__main__":
    main()