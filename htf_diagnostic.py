#!/usr/bin/env python3
"""
HTF Diagnostic System - Data Cleanup and HTF Event Extraction
Based on Grok 4 and Opus 4 guidance for resolving session-HTF temporal causality issues.
"""

import json
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict
from pathlib import Path
import glob
from typing import Dict, List, Any, Tuple, Optional

# Session schedule with corrected Asia time
SESSION_SCHEDULES = {
    'Asia': (19.0, 23.9833),      # 19:00-23:59 ET
    'Midnight': (0.0, 0.4833),    # 00:00-00:29 ET  
    'London': (2.0, 4.9833),      # 02:00-04:59 ET
    'Premarket': (7.0, 9.4833),   # 07:00-09:29 ET
    'NY_AM': (9.5, 11.9833),      # 09:30-11:59 ET
    'Lunch': (12.0, 12.9833),     # 12:00-12:59 ET
    'NY_PM': (13.5, 16.15)        # 13:30-16:09 ET
}

class HTFDiagnosticSystem:
    def __init__(self, base_dir: str = "/Users/<USER>/grok-claude-automation"):
        self.base_dir = Path(base_dir)
        self.beta_h = 0.00442  # HTF decay rate from forensics
        
    def load_all_session_files(self) -> List[Dict[str, Any]]:
        """Load all session JSON files from the directory and subdirectories."""
        session_files = []
        
        # Search patterns for session files in multiple locations
        search_patterns = [
            str(self.base_dir / "*Lvl-1*.json"),
            str(self.base_dir / "data" / "preprocessing" / "level_1" / "*Lvl-1*.json"),
            str(self.base_dir / "**" / "*Lvl-1*.json"),  # Recursive search in project
            "/Users/<USER>/Desktop/*l1*.json",  # Desktop session files
            "/Users/<USER>/Desktop/*session*.json",  # Desktop session files  
            "/Users/<USER>/Desktop/*_Lvl-1_*.json",  # Desktop Lvl-1 files
            "/Users/<USER>/Desktop/*level1*.json"  # Desktop level1 files
        ]
        
        for pattern in search_patterns:
            for file_path in glob.glob(pattern, recursive=True):
                try:
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                        
                    # Extract session info
                    metadata = data.get('session_metadata', {})
                    session_info = {
                        'file_path': file_path,
                        'session_id': metadata.get('session_id', ''),
                        'session_type': metadata.get('session_type', ''),
                        'date': metadata.get('date', ''),
                        'start_time': metadata.get('start_time', ''),
                        'end_time': metadata.get('end_time', ''),
                        'duration_minutes': metadata.get('duration_minutes', 0),
                        'data': data
                    }
                    session_files.append(session_info)
                    
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
                    continue
        
        # Remove duplicates based on file path
        seen_paths = set()
        unique_sessions = []
        for session in session_files:
            if session['file_path'] not in seen_paths:
                unique_sessions.append(session)
                seen_paths.add(session['file_path'])
                
        return unique_sessions
    
    def load_all_htf_tracker_files(self) -> List[Dict[str, Any]]:
        """Load all HTF_Context tracker files chronologically."""
        htf_files = []
        
        # Search patterns for HTF tracker files in multiple locations  
        htf_patterns = [
            str(self.base_dir / "HTF_Context*.json"),
            str(self.base_dir / "**" / "HTF_Context*.json"),  # Recursive in project
            str(self.base_dir / "**" / "HTF_Tracker*.json"),  # HTF Tracker files
            "/Users/<USER>/Desktop/htf_context*.json",  # Desktop HTF files
            "/Users/<USER>/Desktop/*htf*.json"  # Desktop HTF files
        ]
        
        for pattern in htf_patterns:
            for file_path in glob.glob(pattern, recursive=True):
                try:
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                        
                    # Extract timestamp from filename or data
                    timestamp = self._extract_timestamp_from_filename(file_path)
                    
                    htf_info = {
                        'file_path': file_path,
                        'timestamp': timestamp,
                        'data': data
                    }
                    htf_files.append(htf_info)
                    
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
                    continue
        
        # Remove duplicates based on file path
        seen_paths = set()
        unique_htf_files = []
        for htf_file in htf_files:
            if htf_file['file_path'] not in seen_paths:
                unique_htf_files.append(htf_file)
                seen_paths.add(htf_file['file_path'])
        
        # Sort chronologically
        unique_htf_files.sort(key=lambda x: x['timestamp'])
        return unique_htf_files
    
    def _extract_timestamp_from_filename(self, file_path: str) -> str:
        """Extract timestamp from HTF_Context filename."""
        import re
        
        # Look for date pattern in filename
        date_match = re.search(r'(\d{4}-\d{2}-\d{2})', file_path)
        if date_match:
            return f"{date_match.group(1)}T00:00:00"
        
        # Fallback to file modification time
        return datetime.fromtimestamp(Path(file_path).stat().st_mtime).isoformat()
    
    def validate_session_count(self, session_files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Diagnose session count anomaly (121 vs ~70 expected)."""
        unique_sessions = {}
        duplicates = []
        invalid = []
        
        for session in session_files:
            # Create unique identifier
            session_key = f"{session['date']}_{session['session_type']}"
            duration = session['duration_minutes']
            
            if session_key in unique_sessions:
                # Check if it's a true duplicate or variant
                if 'monte_carlo' in session['file_path']:
                    duplicates.append({
                        'type': 'monte_carlo_variant',
                        'file': session['file_path'],
                        'session_id': session['session_id']
                    })
                else:
                    duplicates.append({
                        'type': 'duplicate',
                        'file': session['file_path'], 
                        'session_id': session['session_id']
                    })
            elif duration < 10:
                invalid.append({
                    'reason': 'too_short',
                    'duration': duration,
                    'file': session['file_path'],
                    'session_id': session['session_id']
                })
            else:
                unique_sessions[session_key] = session
        
        return {
            'total_files_found': len(session_files),
            'unique_sessions': len(unique_sessions),
            'duplicate_count': len(duplicates),
            'invalid_count': len(invalid),
            'expected_count': 70,  # ~10 days * 7 sessions per day
            'details': {
                'duplicates': duplicates,
                'invalid': invalid,
                'unique_session_keys': list(unique_sessions.keys())
            },
            'diagnosis': {
                'has_duplicates': len(duplicates) > 0,
                'has_invalid': len(invalid) > 0,
                'count_matches_expected': abs(len(unique_sessions) - 70) < 10
            }
        }
    
    def extract_htf_events_from_trackers(self, htf_tracker_files: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract HTF events from HTF_Context tracker files."""
        htf_events = []
        previous_tracker = None
        
        for tracker_file in htf_tracker_files:
            data = tracker_file['data']
            timestamp = tracker_file['timestamp']
            
            # Extract HTF events from tracker data
            try:
                # Current HTF influence factor - changes indicate HTF events
                current_htf_factor = data.get('htf_influence_factor', 0.315)
                
                # If we have a previous tracker, compare for changes
                if previous_tracker:
                    prev_htf_factor = previous_tracker['data'].get('htf_influence_factor', 0.315) 
                    
                    # Significant HTF factor change indicates HTF event
                    htf_change = abs(current_htf_factor - prev_htf_factor)
                    if htf_change > 0.05:  # >5% change
                        htf_events.append({
                            'time': timestamp,
                            'type': 'htf_influence_change',
                            'price': self._estimate_price_from_structures(data.get('active_structures', [])),
                            'magnitude': htf_change * 10,  # Scale to match forensics magnitude range
                            'htf_factor_change': current_htf_factor - prev_htf_factor,
                            'source_file': tracker_file['file_path']
                        })
                
                # Look for high-strength active structures (session highs/lows)
                active_structures = data.get('active_structures', [])
                for structure in active_structures:
                    strength = structure.get('strength', 0)
                    structure_type = structure.get('structure_type', '')
                    
                    # Session highs/lows with high strength are potential HTF events
                    if (strength >= 0.8 and 
                        structure_type in ['session_high', 'session_low'] and
                        structure.get('validation_strength', 0) >= 1.0):
                        
                        htf_events.append({
                            'time': structure.get('created_timestamp', timestamp),
                            'type': f'htf_{structure_type}',
                            'price': structure.get('level', 0),
                            'magnitude': strength * structure.get('validation_strength', 1.0),
                            'structure_info': structure_type,
                            'source_file': tracker_file['file_path']
                        })
                
                # Look for strong FVG structures that could influence across sessions
                for structure in active_structures:
                    strength = structure.get('strength', 0)
                    structure_type = structure.get('structure_type', '')
                    
                    if (strength >= 0.9 and 
                        'fpfvg' in structure_type and
                        structure.get('recency_factor', 0) >= 0.9):
                        
                        htf_events.append({
                            'time': structure.get('created_timestamp', timestamp),
                            'type': 'htf_fvg_formation',
                            'price': structure.get('level', 0),
                            'magnitude': strength * structure.get('recency_factor', 1.0),
                            'structure_info': structure_type,
                            'source_file': tracker_file['file_path']
                        })
                        
                previous_tracker = tracker_file
                        
            except Exception as e:
                print(f"Error extracting HTF events from {tracker_file['file_path']}: {e}")
                continue
        
        return htf_events
    
    def extract_htf_events_from_sessions(self, session_files: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract HTF events directly from session files, especially Friday sessions."""
        session_htf_events = []
        
        for session_info in session_files:
            try:
                session_data = session_info['data']
                session_type = session_info['session_type']
                session_date = session_info['date']
                
                # Focus on Friday sessions (July 21, 22) and high-impact sessions
                is_friday = self._is_friday_session(session_date)
                is_high_impact = session_type in ['NY_PM', 'London', 'Asia']
                
                if is_friday or is_high_impact:
                    # Extract potential HTF events from price movements
                    events = self._extract_htf_events_from_price_data(session_data, session_info)
                    session_htf_events.extend(events)
                    
            except Exception as e:
                print(f"Error extracting HTF events from session {session_info.get('session_id', 'unknown')}: {e}")
                continue
        
        return session_htf_events
    
    def _is_friday_session(self, date_str: str) -> bool:
        """Check if session date is a Friday."""
        try:
            from datetime import datetime
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            return date_obj.weekday() == 4  # Friday = 4
        except:
            return False
    
    def _extract_htf_events_from_price_data(self, session_data: Dict[str, Any], 
                                          session_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract HTF events from session price movements and structures."""
        htf_events = []
        
        try:
            price_movements = session_data.get('price_movements', [])
            session_metadata = session_data.get('session_metadata', {})
            price_data = session_data.get('price_data', {})
            
            session_type = session_info['session_type']
            session_date = session_info['date']
            
            # Look for significant price movements that could be HTF events
            session_high = price_data.get('high', 0)
            session_low = price_data.get('low', 0)
            session_range = price_data.get('range', 0)
            
            # HTF Event 1: Session High/Low formation (potential weekly levels)
            if session_high > 0:
                htf_events.append({
                    'time': f"{session_date}T{session_metadata.get('start_time', '00:00:00')}",
                    'type': 'session_high_htf',
                    'price': session_high,
                    'magnitude': min(2.0, session_range / 50.0),  # Scale magnitude
                    'structure_info': f'{session_type}_session_high',
                    'source_file': session_info['file_path'],
                    'source_type': 'session_direct'
                })
            
            if session_low > 0:
                htf_events.append({
                    'time': f"{session_date}T{session_metadata.get('end_time', '23:59:00')}",
                    'type': 'session_low_htf',
                    'price': session_low,
                    'magnitude': min(2.0, session_range / 50.0),
                    'structure_info': f'{session_type}_session_low',
                    'source_file': session_info['file_path'],
                    'source_type': 'session_direct'
                })
            
            # HTF Event 2: Significant price movements during session
            for i, movement in enumerate(price_movements):
                if self._is_htf_significant_movement(movement, price_movements, i):
                    timestamp = movement.get('timestamp', '12:00:00')
                    htf_events.append({
                        'time': f"{session_date}T{timestamp}",
                        'type': 'htf_price_movement',
                        'price': movement.get('price', 0),
                        'magnitude': self._calculate_movement_magnitude(movement, price_data),
                        'structure_info': movement.get('context', 'significant_movement'),
                        'source_file': session_info['file_path'],
                        'source_type': 'session_direct'
                    })
            
            # HTF Event 3: Friday-specific events (13:00-24:00 window)
            if self._is_friday_session(session_date):
                friday_events = self._extract_friday_htf_events(session_data, session_info)
                htf_events.extend(friday_events)
                
        except Exception as e:
            print(f"Error processing price data: {e}")
        
        return htf_events
    
    def _is_htf_significant_movement(self, movement: Dict[str, Any], 
                                   all_movements: List[Dict[str, Any]], index: int) -> bool:
        """Determine if a price movement is HTF-significant."""
        context = movement.get('context', '').lower()
        action = movement.get('action', '')
        
        # HTF significance indicators
        htf_keywords = [
            'weekly', 'daily', 'major', 'breakout', 'reversal', 'trap', 'sweep',
            'liquidity', 'session high', 'session low', 'key level'
        ]
        
        return (action in ['delivery', 'touch'] and 
                any(keyword in context for keyword in htf_keywords))
    
    def _calculate_movement_magnitude(self, movement: Dict[str, Any], 
                                    price_data: Dict[str, Any]) -> float:
        """Calculate magnitude of price movement for HTF classification."""
        try:
            price = movement.get('price', 0)
            session_range = price_data.get('range', 100)
            session_high = price_data.get('high', price + 50)
            session_low = price_data.get('low', price - 50)
            
            # Magnitude based on position within session range
            if session_range > 0:
                position_in_range = abs(price - session_low) / session_range
                # Higher magnitude for extreme positions
                if position_in_range > 0.8 or position_in_range < 0.2:
                    return min(3.0, session_range / 30.0)
                else:
                    return min(1.5, session_range / 60.0)
            else:
                return 1.0
                
        except Exception:
            return 1.0
    
    def _extract_friday_htf_events(self, session_data: Dict[str, Any], 
                                 session_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract Friday-specific HTF events in 13:00-24:00 window."""
        friday_events = []
        
        try:
            session_metadata = session_data.get('session_metadata', {})
            session_type = session_info['session_type']
            session_date = session_info['date']
            
            # For Friday sessions, create HTF events in afternoon/evening
            if session_type in ['NY_PM', 'London'] and self._is_friday_session(session_date):
                # Friday PM session creates strong HTF influence for weekend carryover
                friday_events.append({
                    'time': f"{session_date}T15:00:00",  # 3 PM Friday
                    'type': 'friday_weekly_setup',
                    'price': session_data.get('price_data', {}).get('close', 23500),
                    'magnitude': 2.5,  # Strong Friday influence
                    'structure_info': 'friday_weekly_liquidity_setup',
                    'source_file': session_info['file_path'],
                    'source_type': 'friday_specific'
                })
                
                # Friday close HTF event
                friday_events.append({
                    'time': f"{session_date}T17:00:00",  # 5 PM Friday close
                    'type': 'friday_close_htf',
                    'price': session_data.get('price_data', {}).get('close', 23500),
                    'magnitude': 3.0,  # Very strong Friday close influence
                    'structure_info': 'friday_weekly_close',
                    'source_file': session_info['file_path'],
                    'source_type': 'friday_specific'
                })
                
        except Exception as e:
            print(f"Error extracting Friday HTF events: {e}")
        
        return friday_events
    
    def _estimate_price_from_structures(self, structures: List[Dict[str, Any]]) -> float:
        """Estimate representative price from active structures."""
        if not structures:
            return 0.0
        
        # Use the highest strength structure's price
        strongest_structure = max(structures, key=lambda s: s.get('strength', 0))
        return strongest_structure.get('level', 0.0)
    
    def find_session_relevant_htf_events(self, htf_events: List[Dict[str, Any]], 
                                       target_session: str) -> List[Dict[str, Any]]:
        """Filter HTF events relevant to a session's influence window."""
        if target_session not in SESSION_SCHEDULES:
            return []
            
        start_hour, end_hour = SESSION_SCHEDULES[target_session]
        influence_window_hours = 6  # HTF events can influence up to 6 hours later
        
        relevant_events = []
        
        for event in htf_events:
            try:
                # Clean timestamp format (remove " ET" suffix if present)
                clean_time = event['time'].replace(' ET', '').replace('Z', '+00:00')
                event_time = datetime.fromisoformat(clean_time)
                event_hour = event_time.hour + event_time.minute / 60.0
                event_weekday = event_time.weekday()  # 0=Monday, 4=Friday
                
                # Special handling for Asia session (Sunday evening, influenced by Friday)
                if target_session == 'Asia':
                    # Asia session needs Friday 13:00-24:00 events
                    if event_weekday == 4:  # Friday
                        if 13.0 <= event_hour <= 24.0:
                            # Calculate decay over weekend (51 hours)
                            weekend_decay = np.exp(-self.beta_h * 51)
                            if weekend_decay > 0.1:  # >10% influence retained
                                relevant_events.append({
                                    **event,
                                    'session_influenced': target_session,
                                    'influence_strength': weekend_decay,
                                    'delay_hours': 51 + (19.0 - event_hour)  # Friday to Sunday Asia
                                })
                
                # Standard same-day influence
                else:
                    # Check if HTF event can influence this session
                    time_to_session_start = start_hour - event_hour
                    time_to_session_end = end_hour - event_hour
                    
                    # Event must occur before session end and have sufficient influence
                    if time_to_session_end > 0:
                        decay_at_session_start = np.exp(-self.beta_h * max(0, time_to_session_start))
                        if decay_at_session_start > 0.1:  # >10% influence
                            relevant_events.append({
                                **event,
                                'session_influenced': target_session,
                                'influence_strength': decay_at_session_start,
                                'delay_hours': max(0, time_to_session_start)
                            })
                            
            except Exception as e:
                print(f"Error processing HTF event timing: {e}")
                continue
        
        return relevant_events
    
    def generate_session_coverage_report(self, session_files: List[Dict[str, Any]], 
                                       htf_events: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate comprehensive session-HTF coverage analysis."""
        coverage_report = {}
        
        for session_type in SESSION_SCHEDULES.keys():
            # Find relevant HTF events
            relevant_events = self.find_session_relevant_htf_events(htf_events, session_type)
            
            # Count actual sessions of this type
            session_count = len([s for s in session_files if s['session_type'] == session_type])
            
            coverage_report[session_type] = {
                'session_count': session_count,
                'htf_events_available': len(relevant_events),
                'events_per_session': len(relevant_events) / max(1, session_count),
                'has_sufficient_data': len(relevant_events) >= 3,
                'relevant_events': relevant_events[:5],  # Sample of events
                'coverage_quality': self._assess_coverage_quality(len(relevant_events), session_count)
            }
        
        return coverage_report
    
    def _assess_coverage_quality(self, event_count: int, session_count: int) -> str:
        """Assess the quality of HTF event coverage for a session type."""
        if event_count == 0:
            return "no_coverage"
        elif event_count < 3:
            return "insufficient_data"
        elif event_count / max(1, session_count) < 0.3:
            return "sparse_coverage" 
        elif event_count / max(1, session_count) > 0.7:
            return "excellent_coverage"
        else:
            return "adequate_coverage"
    
    def generate_cleaned_session_list(self, session_files: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate cleaned list of unique, valid sessions."""
        cleaned_sessions = []
        seen_sessions = set()
        
        for session in session_files:
            session_key = f"{session['date']}_{session['session_type']}"
            
            # Skip duplicates, monte carlo variants, and invalid sessions
            if (session_key not in seen_sessions and 
                session['duration_minutes'] >= 10 and
                'monte_carlo' not in session['file_path']):
                
                cleaned_sessions.append(session)
                seen_sessions.add(session_key)
        
        return cleaned_sessions
    
    def run_full_diagnostic(self) -> Dict[str, Any]:
        """Run complete diagnostic analysis."""
        print("🔍 Starting HTF Diagnostic Analysis...")
        
        # Step 1: Load data
        print("📂 Loading session files...")
        session_files = self.load_all_session_files()
        print(f"   Found {len(session_files)} session files")
        
        print("📂 Loading HTF tracker files...")
        htf_tracker_files = self.load_all_htf_tracker_files()
        print(f"   Found {len(htf_tracker_files)} HTF tracker files")
        
        # Step 2: Validate session count
        print("🧹 Analyzing session data quality...")
        session_diagnostic = self.validate_session_count(session_files)
        
        # Step 3: Extract HTF events from trackers
        print("⚡ Extracting HTF events from trackers...")
        tracker_htf_events = self.extract_htf_events_from_trackers(htf_tracker_files)
        print(f"   Extracted {len(tracker_htf_events)} HTF events from trackers")
        
        # Step 3b: Extract HTF events directly from sessions (especially Friday)
        print("🔍 Extracting HTF events directly from sessions...")
        session_htf_events = self.extract_htf_events_from_sessions(session_files)
        print(f"   Extracted {len(session_htf_events)} HTF events from sessions")
        
        # Combine both sources
        htf_events = tracker_htf_events + session_htf_events
        print(f"   Total HTF events: {len(htf_events)}")
        
        # Step 4: Generate coverage report
        print("📊 Analyzing session-HTF coverage...")
        coverage_report = self.generate_session_coverage_report(session_files, htf_events)
        
        # Step 5: Generate cleaned session list
        print("✨ Generating cleaned session list...")
        cleaned_sessions = self.generate_cleaned_session_list(session_files)
        print(f"   {len(cleaned_sessions)} unique, valid sessions")
        
        # Compile full report
        diagnostic_report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_files_found': len(session_files),
                'unique_sessions': len(cleaned_sessions),
                'htf_events_extracted': len(htf_events),
                'htf_tracker_files': len(htf_tracker_files)
            },
            'session_diagnostic': session_diagnostic,
            'htf_events': htf_events,
            'coverage_report': coverage_report,
            'cleaned_sessions': [
                {
                    'session_id': s['session_id'],
                    'session_type': s['session_type'], 
                    'date': s['date'],
                    'file_path': s['file_path']
                } for s in cleaned_sessions
            ],
            'recommendations': self._generate_recommendations(session_diagnostic, coverage_report)
        }
        
        return diagnostic_report
    
    def _generate_recommendations(self, session_diagnostic: Dict[str, Any], 
                                coverage_report: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations based on diagnostic results."""
        recommendations = []
        
        # Session data recommendations
        if session_diagnostic['duplicate_count'] > 0:
            recommendations.append(f"Remove {session_diagnostic['duplicate_count']} duplicate session files")
        
        if session_diagnostic['invalid_count'] > 0:
            recommendations.append(f"Remove {session_diagnostic['invalid_count']} invalid session files (<10 min duration)")
        
        # HTF coverage recommendations
        insufficient_sessions = [k for k, v in coverage_report.items() 
                               if v['coverage_quality'] in ['no_coverage', 'insufficient_data']]
        
        if insufficient_sessions:
            recommendations.append(f"Find additional HTF events for sessions: {', '.join(insufficient_sessions)}")
        
        # Specific session recommendations
        if coverage_report.get('Asia', {}).get('htf_events_available', 0) == 0:
            recommendations.append("Search for Friday 13:00-24:00 HTF events to influence Asia sessions")
        
        if coverage_report.get('London', {}).get('htf_events_available', 0) < 3:
            recommendations.append("Search for early morning (02:00-05:00) HTF events for London sessions")
        
        return recommendations


def main():
    """Main execution function."""
    diagnostic_system = HTFDiagnosticSystem()
    
    # Run full diagnostic
    report = diagnostic_system.run_full_diagnostic()
    
    # Save report
    output_file = Path("/Users/<USER>/grok-claude-automation/htf_diagnostic_report.json")
    with open(output_file, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"\n📋 Full diagnostic report saved to: {output_file}")
    
    # Print summary
    print("\n" + "="*60)
    print("HTF DIAGNOSTIC SUMMARY")
    print("="*60)
    
    summary = report['summary']
    print(f"📁 Session Files Found: {summary['total_files_found']}")
    print(f"✅ Unique Valid Sessions: {summary['unique_sessions']}")
    print(f"⚡ HTF Events Extracted: {summary['htf_events_extracted']}")
    print(f"📊 HTF Tracker Files: {summary['htf_tracker_files']}")
    
    print("\n📊 SESSION COVERAGE:")
    for session_type, coverage in report['coverage_report'].items():
        quality = coverage['coverage_quality']
        events = coverage['htf_events_available']
        sessions = coverage['session_count']
        print(f"   {session_type:10}: {events:2d} events, {sessions:2d} sessions - {quality}")
    
    print("\n💡 RECOMMENDATIONS:")
    for i, rec in enumerate(report['recommendations'], 1):
        print(f"   {i}. {rec}")
    
    print("\n✅ Diagnostic complete!")


if __name__ == "__main__":
    main()