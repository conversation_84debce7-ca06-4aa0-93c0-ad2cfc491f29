#!/usr/bin/env python3
"""
Dynamic Live Demo - Real HTF Data + New Data Integration
Shows how algorithm dynamically adapts when you give it new data tomorrow.
"""

import json
import math
from datetime import datetime, timedelta
from pathlib import Path

class LiveDynamicDemo:
    """Demonstrate live dynamic operation with existing HTF data + new data."""
    
    def __init__(self):
        self.base_dir = Path("/Users/<USER>/grok-claude-automation")
        self.htf_params = {"mu_h": 0.02, "alpha_h": 35.51, "beta_h": 0.00442}
        
        # Load existing HTF events (from July 28-29)
        self.existing_htf_events = self._load_existing_htf_events()
        
    def _load_existing_htf_events(self):
        """Load existing HTF events from July 28-29 data."""
        return [
            {
                "time": datetime(2025, 7, 28, 17, 0, 0),
                "magnitude": 3.5,
                "event_type": "friday_close_htf",
                "price": 23496.50
            },
            {
                "time": datetime(2025, 7, 29, 12, 59, 0),
                "magnitude": 2.5,
                "event_type": "structural_completion",
                "price": 23508.0
            },
            {
                "time": datetime(2025, 7, 29, 13, 2, 0),
                "magnitude": 2.2,
                "event_type": "session_high_htf",
                "price": 23515.375
            }
        ]
    
    def calculate_htf_intensity(self, current_time, htf_events):
        """Calculate HTF intensity with given events."""
        intensity = self.htf_params["mu_h"]
        
        for event in htf_events:
            delta_t = (current_time - event["time"]).total_seconds() / 3600.0
            if delta_t >= 0 and delta_t <= 168:  # Within 1 week
                contribution = (self.htf_params["alpha_h"] * 
                              math.exp(-self.htf_params["beta_h"] * delta_t) * 
                              event["magnitude"])
                intensity += contribution
        
        return intensity
    
    def demonstrate_live_dynamic_operation(self):
        """Show how algorithm works with new data tomorrow."""
        print("🌟 LIVE DYNAMIC ALGORITHM DEMONSTRATION")
        print("=" * 65)
        print("Scenario: It's August 1, 2025 - You provide new session data")
        
        # Current time (simulated as August 1, 10:30 AM)
        current_time = datetime(2025, 8, 1, 10, 30, 0)
        
        print(f"\n📅 Current Time: {current_time.strftime('%Y-%m-%d %H:%M')} ET")
        
        # Step 1: Check current HTF intensity with existing events
        print(f"\n📊 STEP 1: Current HTF State")
        current_intensity = self.calculate_htf_intensity(current_time, self.existing_htf_events)
        
        print(f"   Existing HTF Events: {len(self.existing_htf_events)}")
        for event in self.existing_htf_events:
            age_hours = (current_time - event["time"]).total_seconds() / 3600.0
            decay = math.exp(-self.htf_params["beta_h"] * age_hours)
            contribution = self.htf_params["alpha_h"] * decay * event["magnitude"]
            print(f"     • {event['time'].strftime('%m/%d %H:%M')}: {contribution:.2f} intensity ({age_hours:.1f}h ago)")
        
        print(f"   Current HTF Intensity: {current_intensity:.4f}")
        print(f"   Activation Threshold: 0.5000")
        print(f"   Status: {'🚀 ACTIVE' if current_intensity > 0.5 else '🛌 DORMANT'}")
        
        # Step 2: You provide new data (London session from August 1)
        print(f"\n📥 STEP 2: You Provide New Data")
        new_session_data = {
            "session_metadata": {
                "session_type": "London",
                "date": "2025-08-01",
                "start_time": "02:00:00 ET"
            },
            "price_movements": [
                {
                    "timestamp": "02:45:00",
                    "price": 23650.0,
                    "action": "touch",
                    "context": "London session high created, weekly resistance test"
                },
                {
                    "timestamp": "03:20:00", 
                    "price": 23620.5,
                    "action": "delivery",
                    "context": "Major liquidity sweep, institutional order flow"
                },
                {
                    "timestamp": "04:15:00",
                    "price": 23595.25,
                    "action": "touch",
                    "context": "London session low created, reversal structure"
                }
            ]
        }
        
        print(f"   📁 NEW SESSION DATA RECEIVED:")
        print(f"      Session: {new_session_data['session_metadata']['session_type']}")
        print(f"      Date: {new_session_data['session_metadata']['date']}")
        print(f"      Events: {len(new_session_data['price_movements'])} price movements")
        
        # Step 3: Algorithm automatically extracts HTF events from new data
        print(f"\n🔍 STEP 3: Algorithm Extracts New HTF Events")
        new_htf_events = []
        
        for movement in new_session_data["price_movements"]:
            context = movement["context"].lower()
            
            # Determine if movement is HTF-significant
            if any(keyword in context for keyword in ["session high", "session low", "liquidity sweep", "weekly"]):
                magnitude = 2.0  # Base magnitude
                if "session high" in context or "session low" in context:
                    magnitude += 1.0
                if "liquidity sweep" in context:
                    magnitude += 0.8
                if "weekly" in context:
                    magnitude += 0.5
                
                new_event = {
                    "time": datetime(2025, 8, 1, int(movement["timestamp"][:2]), int(movement["timestamp"][3:5]), 0),
                    "magnitude": magnitude,
                    "event_type": "session_high_htf" if "high" in context else "htf_liquidity_event",
                    "price": movement["price"]
                }
                new_htf_events.append(new_event)
                
                print(f"   ✅ HTF EVENT EXTRACTED:")
                print(f"      Time: {new_event['time'].strftime('%H:%M')} ET")
                print(f"      Type: {new_event['event_type']}")
                print(f"      Price: {new_event['price']}")
                print(f"      Magnitude: {magnitude:.1f}")
        
        # Step 4: Recalculate HTF intensity with all events
        print(f"\n⚡ STEP 4: Dynamic HTF Intensity Update")
        all_htf_events = self.existing_htf_events + new_htf_events
        updated_intensity = self.calculate_htf_intensity(current_time, all_htf_events)
        
        print(f"   Previous Intensity: {current_intensity:.4f}")
        print(f"   New Events Added: {len(new_htf_events)}")
        print(f"   UPDATED INTENSITY: {updated_intensity:.4f}")
        print(f"   Intensity Change: +{updated_intensity - current_intensity:.4f}")
        
        activation_status = "🚀 ACTIVATED" if updated_intensity > 0.5 else "🛌 DORMANT"
        print(f"   Activation Status: {activation_status}")
        
        # Step 5: Generate dynamic prediction if activated
        if updated_intensity > 0.5:
            print(f"\n🎯 STEP 5: Dynamic Prediction Generation")
            
            enhancement_factor = updated_intensity / 0.5
            target_sessions = ["NY_AM", "Lunch"]  # Based on current time
            
            # Session-level calculation
            session_baseline = 0.163
            session_threshold = 0.245
            current_session_intensity = 0.19  # Example current state
            
            # Apply HTF enhancement
            enhanced_baseline = session_baseline * enhancement_factor
            enhanced_threshold = session_threshold / min(1.5, enhancement_factor ** 0.5)
            
            # Calculate cascade timing
            if current_session_intensity >= enhanced_threshold:
                time_to_cascade = 0
            else:
                intensity_gap = enhanced_threshold - current_session_intensity
                buildup_rate = 0.01 * min(1.5, enhancement_factor ** 0.5)
                time_to_cascade = intensity_gap / buildup_rate
            
            predicted_time = current_time + timedelta(minutes=time_to_cascade)
            confidence = min(0.95, 0.85 * min(1.5, enhancement_factor ** 0.5))
            
            print(f"   ✅ DYNAMIC PREDICTION GENERATED:")
            print(f"      HTF Enhancement: {enhancement_factor:.2f}x")
            print(f"      Target Sessions: {target_sessions}")
            print(f"      Predicted Time: {predicted_time.strftime('%H:%M:%S')} ET")
            print(f"      Minutes from now: {time_to_cascade:.1f}")
            print(f"      Confidence: {confidence:.1%}")
            print(f"      Method: Dynamic HTF-Session Integration")
            
        else:
            print(f"\n🛌 STEP 5: No Prediction Generated")
            print(f"   HTF intensity ({updated_intensity:.4f}) below threshold (0.5)")
            print(f"   System remains dormant until more significant HTF events occur")
        
        # Step 6: Show continuous adaptation capability
        print(f"\n🔄 STEP 6: Continuous Adaptation Ready")
        print(f"   ✅ Algorithm ready for next data update")
        print(f"   ✅ HTF events automatically tracked and aged")
        print(f"   ✅ Intensity continuously recalculated")
        print(f"   ✅ Predictions update as new data arrives")
        print(f"   ✅ Session targeting adapts to current time")
        
        print(f"\n🎯 DYNAMIC OPERATION SUMMARY:")
        print(f"   📊 Started with {len(self.existing_htf_events)} existing HTF events")
        print(f"   📥 Processed new session data automatically")
        print(f"   🔍 Extracted {len(new_htf_events)} new HTF events")
        print(f"   ⚡ Updated HTF intensity: {current_intensity:.4f} → {updated_intensity:.4f}")
        
        if updated_intensity > 0.5:
            print(f"   🚀 Generated real-time prediction with {enhancement_factor:.1f}x enhancement")
        
        print(f"\n💡 KEY INSIGHTS:")
        print(f"   🔄 Algorithm is FULLY DYNAMIC - adapts to any new data")
        print(f"   ⏰ Real-time HTF intensity calculation")
        print(f"   🎯 Automatic HTF event extraction from new sessions")
        print(f"   🚀 Dynamic prediction generation when thresholds met")
        print(f"   📈 Continuous learning and adaptation")
        
        return {
            "initial_intensity": current_intensity,
            "final_intensity": updated_intensity,
            "new_events_extracted": len(new_htf_events),
            "prediction_generated": updated_intensity > 0.5,
            "enhancement_factor": updated_intensity / 0.5 if updated_intensity > 0.5 else 0
        }


def main():
    """Run live dynamic demonstration."""
    demo = LiveDynamicDemo()
    results = demo.demonstrate_live_dynamic_operation()
    
    print(f"\n💾 Demo completed - Algorithm ready for live operation!")
    
    return results


if __name__ == "__main__":
    main()