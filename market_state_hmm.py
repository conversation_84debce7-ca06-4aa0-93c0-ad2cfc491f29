#!/usr/bin/env python3
"""
Market State Hidden Markov Model
Detects market states (consolidating → pre_cascade → expanding → exhausted) 
for event timing prediction using energy, T_memory, and FVG dynamics.
"""

import numpy as np
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import math
import sys
import os
sys.path.append('.')
from src.utils import load_json_data

class MarketState(Enum):
    """Market state enumeration for HMM"""
    CONSOLIDATING = "consolidating"
    PRE_CASCADE = "pre_cascade" 
    EXPANDING = "expanding"
    EXHAUSTED = "exhausted"

@dataclass
class StateMetrics:
    """Metrics for each market state"""
    energy_level: float
    volatility: float
    t_memory: float
    fvg_cluster_density: float
    momentum_strength: float
    proximity_to_half_hour: float

@dataclass
class StateTransition:
    """State transition with timing prediction"""
    from_state: MarketState
    to_state: MarketState
    probability: float
    expected_time_minutes: float
    confidence: float
    trigger_conditions: Dict[str, float]

@dataclass
class TimingPrediction:
    """Event timing prediction result"""
    event_type: str
    time_window_start: datetime
    time_window_end: datetime
    confidence: float
    state_sequence: List[MarketState]
    energy_threshold: float
    trigger_metrics: Dict[str, float]

class MarketStateHMM:
    """Hidden Markov Model for market state detection and event timing prediction"""
    
    def __init__(self):
        # State transition matrix [from_state][to_state] = probability
        self.transition_matrix = {
            MarketState.CONSOLIDATING: {
                MarketState.CONSOLIDATING: 0.7,
                MarketState.PRE_CASCADE: 0.25,
                MarketState.EXPANDING: 0.05,
                MarketState.EXHAUSTED: 0.0
            },
            MarketState.PRE_CASCADE: {
                MarketState.CONSOLIDATING: 0.1,
                MarketState.PRE_CASCADE: 0.3,
                MarketState.EXPANDING: 0.6,
                MarketState.EXHAUSTED: 0.0
            },
            MarketState.EXPANDING: {
                MarketState.CONSOLIDATING: 0.1,
                MarketState.PRE_CASCADE: 0.0,
                MarketState.EXPANDING: 0.6,
                MarketState.EXHAUSTED: 0.3
            },
            MarketState.EXHAUSTED: {
                MarketState.CONSOLIDATING: 0.8,
                MarketState.PRE_CASCADE: 0.0,
                MarketState.EXPANDING: 0.0,
                MarketState.EXHAUSTED: 0.2
            }
        }
        
        # State thresholds based on user specifications
        self.state_thresholds = {
            MarketState.CONSOLIDATING: {
                'energy_max': 0.3,
                'volatility_max': 0.4,
                't_memory_max': 3.0
            },
            MarketState.PRE_CASCADE: {
                't_memory_min': 5.0,
                'fvg_cluster_density_min': 3.0,
                'energy_range': (0.3, 0.7)
            },
            MarketState.EXPANDING: {
                'energy_rate_min': 0.7,
                'momentum_min': 1.0,
                'volatility_min': 0.6
            },
            MarketState.EXHAUSTED: {
                'energy_max': 0.1,
                'momentum_max': 0.3,
                'post_expansion': True
            }
        }
        
        # Timing patterns (events cluster around :00/:30 marks)
        self.timing_patterns = {
            'half_hour_magnetism': 0.7,  # Strength of :00/:30 attraction
            'cascade_lead_time': 8,      # 8-minute lead time before cascades (12:22 pattern)
            'expansion_duration': 15,    # Average expansion duration
            'consolidation_duration': 25 # Average consolidation duration
        }
        
        # Current state tracking
        self.current_state = MarketState.CONSOLIDATING
        self.state_probability = 1.0
        self.state_duration_minutes = 0
        self.last_update_time = datetime.now()
        
    def calculate_state_metrics(self, session_data: dict, tracker_context: tuple = None) -> StateMetrics:
        """Calculate current state metrics from session data and tracker context"""
        
        # Extract basic session metrics
        price_data = session_data.get('price_data', {})
        session_range = price_data.get('range', 100)
        
        # Extract energy from processed Grok data
        grok_data = session_data.get('grok_enhanced_calculations', {})
        unit_b = grok_data.get('unit_b_energy_structure', {})
        energy_accumulation = unit_b.get('energy_accumulation', {})
        energy_rate = energy_accumulation.get('energy_rate', 0.5)
        
        # Normalize energy level (0-2 range to 0-1)
        energy_level = min(1.0, energy_rate / 2.0)
        
        # Calculate volatility from session range (normalize to 0-1)
        volatility = min(1.0, session_range / 200.0)  # 200+ points = high volatility
        
        # Extract T_memory from tracker context
        t_memory = 5.0  # Default
        fvg_cluster_density = 0.0
        if tracker_context:
            try:
                htf_tracker, fvg_tracker, liquidity_tracker = tracker_context
                t_memory = fvg_tracker.get('t_memory_calculations', {}).get('t_memory_current', 5.0)
                
                # Calculate FVG cluster density from tracker data
                fvg_clusters = fvg_tracker.get('active_fvg_clusters', [])
                fvg_cluster_density = len(fvg_clusters)
            except Exception as e:
                print(f"⚠️ Tracker context extraction failed: {e}")
        
        # Calculate momentum strength from unit_c data
        unit_c = grok_data.get('unit_c_advanced_dynamics', {})
        momentum_data = unit_c.get('temporal_momentum', {})
        momentum_strength = momentum_data.get('momentum_strength', 0.5)
        
        # Normalize momentum (typical range 0-2)
        momentum_strength = min(1.0, momentum_strength / 2.0)
        
        # Calculate proximity to half-hour mark
        current_time = datetime.now()
        minutes_past_hour = current_time.minute
        minutes_to_next_half_hour = 30 - (minutes_past_hour % 30)
        proximity_to_half_hour = 1.0 - (minutes_to_next_half_hour / 30.0)
        
        return StateMetrics(
            energy_level=energy_level,
            volatility=volatility,
            t_memory=t_memory,
            fvg_cluster_density=fvg_cluster_density,
            momentum_strength=momentum_strength,
            proximity_to_half_hour=proximity_to_half_hour
        )
    
    def classify_state(self, metrics: StateMetrics) -> Tuple[MarketState, float]:
        """Classify current market state based on metrics"""
        
        state_scores = {}
        
        # CONSOLIDATING state scoring
        consolidating_score = 0.0
        if metrics.energy_level <= self.state_thresholds[MarketState.CONSOLIDATING]['energy_max']:
            consolidating_score += 0.4
        if metrics.volatility <= self.state_thresholds[MarketState.CONSOLIDATING]['volatility_max']:
            consolidating_score += 0.3
        if metrics.t_memory <= self.state_thresholds[MarketState.CONSOLIDATING]['t_memory_max']:
            consolidating_score += 0.3
        
        state_scores[MarketState.CONSOLIDATING] = consolidating_score
        
        # PRE_CASCADE state scoring
        pre_cascade_score = 0.0
        if metrics.t_memory >= self.state_thresholds[MarketState.PRE_CASCADE]['t_memory_min']:
            pre_cascade_score += 0.4
        if metrics.fvg_cluster_density >= self.state_thresholds[MarketState.PRE_CASCADE]['fvg_cluster_density_min']:
            pre_cascade_score += 0.4
        energy_range = self.state_thresholds[MarketState.PRE_CASCADE]['energy_range']
        if energy_range[0] <= metrics.energy_level <= energy_range[1]:
            pre_cascade_score += 0.2
        
        state_scores[MarketState.PRE_CASCADE] = pre_cascade_score
        
        # EXPANDING state scoring
        expanding_score = 0.0
        if metrics.energy_level >= self.state_thresholds[MarketState.EXPANDING]['energy_rate_min']:
            expanding_score += 0.4
        if metrics.momentum_strength >= self.state_thresholds[MarketState.EXPANDING]['momentum_min']:
            expanding_score += 0.3
        if metrics.volatility >= self.state_thresholds[MarketState.EXPANDING]['volatility_min']:
            expanding_score += 0.3
        
        state_scores[MarketState.EXPANDING] = expanding_score
        
        # EXHAUSTED state scoring (requires previous expanding state)
        exhausted_score = 0.0
        if self.current_state == MarketState.EXPANDING:  # Can only exhaust from expanding
            if metrics.energy_level <= self.state_thresholds[MarketState.EXHAUSTED]['energy_max']:
                exhausted_score += 0.5
            if metrics.momentum_strength <= self.state_thresholds[MarketState.EXHAUSTED]['momentum_max']:
                exhausted_score += 0.5
        
        state_scores[MarketState.EXHAUSTED] = exhausted_score
        
        # Find highest scoring state
        best_state = max(state_scores, key=state_scores.get)
        confidence = state_scores[best_state]
        
        return best_state, confidence
    
    def predict_next_transition(self, current_metrics: StateMetrics) -> Optional[StateTransition]:
        """Predict the next state transition with timing"""
        
        # Get transition probabilities from current state
        possible_transitions = self.transition_matrix[self.current_state]
        
        # Find most likely next state (excluding staying in current state)
        next_states = {state: prob for state, prob in possible_transitions.items() 
                      if state != self.current_state and prob > 0.1}
        
        if not next_states:
            return None
            
        most_likely_next = max(next_states, key=next_states.get)
        transition_probability = next_states[most_likely_next]
        
        # Calculate expected timing based on state dynamics
        expected_time = self._calculate_transition_timing(
            self.current_state, most_likely_next, current_metrics
        )
        
        # Calculate confidence based on metrics alignment
        confidence = self._calculate_transition_confidence(
            most_likely_next, current_metrics, transition_probability
        )
        
        # Identify trigger conditions
        trigger_conditions = self._get_trigger_conditions(most_likely_next, current_metrics)
        
        return StateTransition(
            from_state=self.current_state,
            to_state=most_likely_next,
            probability=transition_probability,
            expected_time_minutes=expected_time,
            confidence=confidence,
            trigger_conditions=trigger_conditions
        )
    
    def _calculate_transition_timing(self, from_state: MarketState, to_state: MarketState, 
                                   metrics: StateMetrics) -> float:
        """Calculate expected time until state transition"""
        
        base_timing = {
            (MarketState.CONSOLIDATING, MarketState.PRE_CASCADE): 20.0,
            (MarketState.PRE_CASCADE, MarketState.EXPANDING): 8.0,  # 12:22 pattern - 8 min lead
            (MarketState.EXPANDING, MarketState.EXHAUSTED): 15.0,
            (MarketState.EXHAUSTED, MarketState.CONSOLIDATING): 10.0
        }
        
        base_time = base_timing.get((from_state, to_state), 15.0)
        
        # Adjust for proximity to half-hour (events accelerate near :00/:30)
        half_hour_factor = 1.0 - (metrics.proximity_to_half_hour * 0.3)
        
        # Adjust for T_memory intensity (higher T_memory = faster transitions)
        t_memory_factor = max(0.5, 1.0 - (metrics.t_memory / 20.0))
        
        # Adjust for energy level
        energy_factor = 1.0
        if to_state == MarketState.EXPANDING:
            energy_factor = max(0.7, 1.0 - metrics.energy_level * 0.3)  # High energy = faster expansion
        
        adjusted_time = base_time * half_hour_factor * t_memory_factor * energy_factor
        
        return max(2.0, adjusted_time)  # Minimum 2 minutes
    
    def _calculate_transition_confidence(self, next_state: MarketState, metrics: StateMetrics,
                                       transition_prob: float) -> float:
        """Calculate confidence in transition prediction"""
        
        # Base confidence from transition probability
        base_confidence = transition_prob
        
        # Boost confidence if metrics strongly support the transition
        metric_support = 0.0
        
        if next_state == MarketState.PRE_CASCADE:
            if metrics.t_memory >= 5.0:
                metric_support += 0.3
            if metrics.fvg_cluster_density >= 3.0:
                metric_support += 0.2
                
        elif next_state == MarketState.EXPANDING:
            if metrics.energy_level >= 0.7:
                metric_support += 0.3
            if metrics.momentum_strength >= 1.0:
                metric_support += 0.2
                
        elif next_state == MarketState.EXHAUSTED:
            if metrics.energy_level <= 0.1:
                metric_support += 0.3
            if self.state_duration_minutes >= 15:  # Expansion duration
                metric_support += 0.2
        
        # Boost confidence near half-hour marks
        timing_support = metrics.proximity_to_half_hour * 0.1
        
        total_confidence = min(0.95, base_confidence + metric_support + timing_support)
        return total_confidence
    
    def _get_trigger_conditions(self, next_state: MarketState, metrics: StateMetrics) -> Dict[str, float]:
        """Get specific trigger conditions for state transition"""
        
        conditions = {}
        
        if next_state == MarketState.PRE_CASCADE:
            conditions['t_memory_threshold'] = 5.0
            conditions['fvg_cluster_min'] = 3.0
            conditions['energy_range_min'] = 0.3
            
        elif next_state == MarketState.EXPANDING:
            conditions['energy_rate_threshold'] = 0.7
            conditions['momentum_threshold'] = 1.0
            conditions['volatility_min'] = 0.6
            
        elif next_state == MarketState.EXHAUSTED:
            conditions['energy_max'] = 0.1
            conditions['momentum_max'] = 0.3
            conditions['duration_min_minutes'] = 15
            
        elif next_state == MarketState.CONSOLIDATING:
            conditions['energy_max'] = 0.3
            conditions['volatility_max'] = 0.4
            conditions['t_memory_max'] = 3.0
        
        return conditions
    
    def predict_event_timing(self, session_data: dict, tracker_context: tuple = None,
                           target_event: str = "cascade") -> Optional[TimingPrediction]:
        """Predict timing of specific market events"""
        
        print(f"🔮 MARKET STATE HMM - EVENT TIMING PREDICTION")
        print("=" * 50)
        
        # Calculate current metrics
        current_metrics = self.calculate_state_metrics(session_data, tracker_context)
        
        # Classify current state
        detected_state, state_confidence = self.classify_state(current_metrics)
        self.current_state = detected_state
        self.state_probability = state_confidence
        
        print(f"📊 Current State: {detected_state.value} (confidence: {state_confidence:.2f})")
        print(f"   Energy Level: {current_metrics.energy_level:.2f}")
        print(f"   T_memory: {current_metrics.t_memory:.1f}")
        print(f"   FVG Clusters: {current_metrics.fvg_cluster_density:.0f}")
        print(f"   Momentum: {current_metrics.momentum_strength:.2f}")
        print(f"   Half-hour Proximity: {current_metrics.proximity_to_half_hour:.2f}")
        
        # Predict next transition
        next_transition = self.predict_next_transition(current_metrics)
        
        if not next_transition:
            print("⚠️ No clear state transition predicted")
            return None
        
        print(f"\n🎯 Next Transition Prediction:")
        print(f"   {next_transition.from_state.value} → {next_transition.to_state.value}")
        print(f"   Expected Time: {next_transition.expected_time_minutes:.1f} minutes")
        print(f"   Confidence: {next_transition.confidence:.2f}")
        
        # Build event timing prediction
        current_time = datetime.now()
        
        # For cascade events, predict through the state sequence
        if target_event == "cascade":
            if detected_state == MarketState.CONSOLIDATING:
                # Need: consolidating → pre_cascade → expanding
                total_time = next_transition.expected_time_minutes + 8.0  # pre_cascade → expanding
                event_type = "cascade_start"
            elif detected_state == MarketState.PRE_CASCADE:
                # Direct transition to expanding (cascade)
                total_time = next_transition.expected_time_minutes
                event_type = "cascade_start"
            elif detected_state == MarketState.EXPANDING:
                # Already cascading
                total_time = 0
                event_type = "cascade_active"
            else:
                # From exhausted, need to consolidate first
                total_time = next_transition.expected_time_minutes + 20.0 + 8.0
                event_type = "future_cascade"
        else:
            # Generic event timing
            total_time = next_transition.expected_time_minutes
            event_type = f"{next_transition.to_state.value}_transition"
        
        # Calculate time windows with ±5 minute tolerance
        time_window_start = current_time + timedelta(minutes=max(0, total_time - 5))
        time_window_end = current_time + timedelta(minutes=total_time + 5)
        
        # Build state sequence prediction
        state_sequence = [detected_state]
        if target_event == "cascade" and detected_state != MarketState.EXPANDING:
            if detected_state == MarketState.CONSOLIDATING:
                state_sequence.extend([MarketState.PRE_CASCADE, MarketState.EXPANDING])
            elif detected_state == MarketState.PRE_CASCADE:
                state_sequence.append(MarketState.EXPANDING)
        
        # Energy threshold for event trigger
        energy_threshold = 0.7 if "cascade" in event_type else current_metrics.energy_level
        
        prediction = TimingPrediction(
            event_type=event_type,
            time_window_start=time_window_start,
            time_window_end=time_window_end,
            confidence=next_transition.confidence,
            state_sequence=state_sequence,
            energy_threshold=energy_threshold,
            trigger_metrics={
                'current_energy': current_metrics.energy_level,
                'current_t_memory': current_metrics.t_memory,
                'current_momentum': current_metrics.momentum_strength,
                'half_hour_proximity': current_metrics.proximity_to_half_hour
            }
        )
        
        print(f"\n⏰ Event Timing Prediction:")
        print(f"   Event: {event_type}")
        print(f"   Time Window: {time_window_start.strftime('%H:%M')} - {time_window_end.strftime('%H:%M')}")
        print(f"   Confidence: {prediction.confidence:.2f}")
        print(f"   State Sequence: {' → '.join([s.value for s in state_sequence])}")
        
        return prediction
    
    def update_state(self, session_data: dict, tracker_context: tuple = None):
        """Update current state based on new data"""
        
        current_time = datetime.now()
        time_elapsed = (current_time - self.last_update_time).total_seconds() / 60.0
        self.state_duration_minutes += time_elapsed
        
        # Calculate new metrics
        metrics = self.calculate_state_metrics(session_data, tracker_context)
        
        # Classify new state
        new_state, confidence = self.classify_state(metrics)
        
        # Check for state change
        if new_state != self.current_state:
            print(f"🔄 State Transition: {self.current_state.value} → {new_state.value}")
            print(f"   Duration in {self.current_state.value}: {self.state_duration_minutes:.1f} minutes")
            
            self.current_state = new_state
            self.state_probability = confidence
            self.state_duration_minutes = 0
        else:
            self.state_probability = confidence
        
        self.last_update_time = current_time

def main():
    """Test the Market State HMM system"""
    
    print("🧪 MARKET STATE HMM TEST")
    print("=" * 35)
    
    # Load test data
    try:
        session_data = load_json_data('NYPM_grokEnhanced_2025_07_23.json')
        fvg_tracker = load_json_data('FVG_Tracker_LUNCH_2025_07_24.json')
        
        # Create minimal tracker context for testing
        tracker_context = ({}, fvg_tracker, {})
        
    except FileNotFoundError as e:
        print(f"❌ Test data not found: {e}")
        return
    
    # Initialize HMM
    hmm = MarketStateHMM()
    
    # Test state detection and event timing prediction
    cascade_prediction = hmm.predict_event_timing(
        session_data, tracker_context, target_event="cascade"
    )
    
    if cascade_prediction:
        print(f"\n✅ Successfully predicted cascade timing!")
        print(f"   Event will occur: {cascade_prediction.time_window_start.strftime('%H:%M')} - {cascade_prediction.time_window_end.strftime('%H:%M')}")
        
        # Test against July 23rd 12:22 cascade (this would be validation)
        actual_cascade_time = "12:22"  # Known from session data
        print(f"   Known cascade occurred at: {actual_cascade_time}")
        print(f"   Prediction success: Would need real-time testing")
    
    # Save test results
    test_results = {
        'test_metadata': {
            'test_type': 'market_state_hmm_prediction',
            'date': '2025_07_23',
            'timestamp': datetime.now().isoformat()
        },
        'state_detection': {
            'current_state': hmm.current_state.value,
            'confidence': hmm.state_probability
        },
        'timing_prediction': {
            'event_type': cascade_prediction.event_type if cascade_prediction else None,
            'time_windows': f"{cascade_prediction.time_window_start.strftime('%H:%M')}-{cascade_prediction.time_window_end.strftime('%H:%M')}" if cascade_prediction else None,
            'confidence': cascade_prediction.confidence if cascade_prediction else None
        }
    }
    
    output_file = f"market_state_hmm_test_{datetime.now().strftime('%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(test_results, f, indent=2, default=str)
    
    print(f"\n💾 Test results saved to: {output_file}")

if __name__ == "__main__":
    main()