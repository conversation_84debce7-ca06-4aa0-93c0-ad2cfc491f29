#!/usr/bin/env python3
"""
Cross-Session Analysis: Asia → London Prediction
Test the complete cross-session prediction system using July 22nd data
"""

import json
import sys
import os
from typing import Dict

# Add src path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'experimental'))

from cross_session_predictor import CrossSessionPredictionSystem

def run_asia_to_london_analysis():
    """Run complete Asia → London cross-session analysis"""
    
    print("🌏 ASIA → LONDON CROSS-SESSION PREDICTION")
    print("=" * 55)
    print("Using Asia session data to predict London session outcomes")
    print("with automatic Grok 4 failure analysis when predictions miss targets\n")
    
    # Initialize cross-session prediction system
    cross_session_system = CrossSessionPredictionSystem(error_threshold=25.0)
    
    # Configuration
    source_session = "asia"
    target_session = "london" 
    date = "2025_07_22"
    
    print(f"📊 Analysis Configuration:")
    print(f"   Source Session: {source_session.title()}")
    print(f"   Target Session: {target_session.title()}")
    print(f"   Date: {date}")
    print(f"   Error Threshold: {cross_session_system.error_threshold} points\n")
    
    try:
        # Run complete cross-session analysis
        print("🚀 Starting cross-session prediction workflow...\n")
        
        results = cross_session_system.run_cross_session_analysis(
            source_session, 
            date, 
            target_session
        )
        
        print(f"\n📊 CROSS-SESSION ANALYSIS RESULTS")
        print("=" * 40)
        
        if "cross_session_prediction" in results:
            prediction = results["cross_session_prediction"]
            
            print(f"🎯 LONDON PREDICTION FROM ASIA DATA:")
            print(f"   Predicted Close: {prediction['predicted_close']:.2f}")
            print(f"   Predicted High: {prediction['predicted_high']:.2f}")
            print(f"   Predicted Low: {prediction['predicted_low']:.2f}")
            print(f"   Predicted Range: {prediction['predicted_range'][1] - prediction['predicted_range'][0]:.2f} points")
            print(f"   Session Character: {prediction['session_character_prediction']}")
            print(f"   Confidence Level: {prediction['confidence_level']:.2f}")
            
            # Display momentum analysis
            if "prediction_metadata" in prediction:
                metadata = prediction["prediction_metadata"]
                print(f"\n🔄 MOMENTUM TRANSFER ANALYSIS:")
                print(f"   Asia Close: {metadata.get('asia_close', 'N/A'):.2f}")
                print(f"   London Open (predicted): {metadata.get('london_open', 'N/A'):.2f}")
                print(f"   Temporal Decay Applied: {metadata.get('temporal_decay_applied', 'N/A'):.3f}")
                print(f"   Character Multiplier: {metadata.get('character_multiplier', 'N/A'):.2f}")
                print(f"   Volatility Estimate: {metadata.get('volatility_estimate', 'N/A'):.4f}")
            
            # Display liquidity interaction forecast
            if "liquidity_interaction_forecast" in prediction:
                liquidity_forecast = prediction["liquidity_interaction_forecast"]
                high_prob_interactions = [
                    interaction for interaction in liquidity_forecast 
                    if interaction["interaction_probability"] > 0.5
                ]
                
                if high_prob_interactions:
                    print(f"\n💧 HIGH-PROBABILITY LIQUIDITY INTERACTIONS:")
                    for interaction in high_prob_interactions[:3]:
                        print(f"   Level {interaction['liquidity_level']:.2f}: {interaction['interaction_type']} "
                              f"({interaction['interaction_probability']:.1%} probability)")
            
            # Display momentum decay timeline
            if "momentum_decay_timeline" in prediction:
                decay_timeline = prediction["momentum_decay_timeline"]
                print(f"\n⏰ MOMENTUM DECAY TIMELINE:")
                for minute, strength in list(decay_timeline.items())[:4]:
                    hours = minute // 60
                    mins = minute % 60
                    print(f"   T+{hours:02d}:{mins:02d}: {strength:.3f} momentum strength")
        
        # Display validation results
        validation = results.get("validation_results", {})
        
        print(f"\n✅ VALIDATION AGAINST ACTUAL LONDON SESSION:")
        
        if validation.get("validation_status") == "no_validation_data":
            print("   ⚠️ London session file not available - prediction only mode")
            print("   📋 Create london_grokEnhanced_2025_07_22.json to enable validation")
            
        elif "prediction_errors" in validation:
            errors = validation["prediction_errors"]
            accuracy = validation["prediction_accuracy"]
            
            print(f"   Validation Status: {validation['validation_status'].upper()}")
            print(f"   Overall Error: {errors['overall_error']:.2f} points ({errors['error_percentage']:.2f}%)")
            print(f"   Close Error: {errors['close_error']:.2f} points")
            print(f"   High Error: {errors['high_error']:.2f} points")
            print(f"   Low Error: {errors['low_error']:.2f} points")
            
            print(f"\n   📊 ACTUAL vs PREDICTED COMPARISON:")
            print(f"      Predicted Close: {accuracy['predicted_close']:.2f} | Actual: {accuracy['actual_close']:.2f}")
            print(f"      Predicted High: {accuracy['predicted_high']:.2f} | Actual: {accuracy['actual_high']:.2f}")
            print(f"      Predicted Low: {accuracy['predicted_low']:.2f} | Actual: {accuracy['actual_low']:.2f}")
            print(f"      Predicted Character: {accuracy['predicted_character']} | Actual: {accuracy['actual_character']}")
            
            # Display failure analysis if triggered
            if validation["validation_status"] == "failed_with_analysis":
                failure_analysis = validation.get("cross_session_failure_analysis")
                if failure_analysis:
                    print(f"\n🚨 GROK 4 FAILURE ANALYSIS TRIGGERED:")
                    print(f"   Failure Type: {failure_analysis['failure_type']}")
                    print(f"   Analysis Timestamp: {failure_analysis['analysis_timestamp']}")
                    
                    if "grok_relationship_discovery" in failure_analysis:
                        grok_analysis = failure_analysis["grok_relationship_discovery"]
                        print(f"   🧮 Mathematical Relationship Discovery: COMPLETED")
                        print(f"      Grok 4 has analyzed the cross-session failure")
                        print(f"      Alternative formulas and corrections generated")
                    elif "grok_analysis_error" in failure_analysis:
                        print(f"   ⚠️ Grok 4 Analysis Error: {failure_analysis['grok_analysis_error']}")
                        print(f"      Manual review of failure context required")
        
        # Save comprehensive results
        output_file = f"cross_session_analysis_asia_to_london_{date}.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Complete analysis saved to: {output_file}")
        
        # Final system summary
        print(f"\n🎉 CROSS-SESSION PREDICTION ANALYSIS COMPLETE")
        print("=" * 50)
        
        print(f"✅ Asia Session Data: Successfully processed with all tracker files")
        print(f"✅ Cross-Session Prediction: London outcomes predicted from Asia momentum")
        print(f"✅ Parameter Transfer: Momentum, liquidity, and character carryover calculated")
        print(f"✅ Temporal Modeling: Decay factors applied for session gap")
        
        if validation.get("validation_status") == "failed_with_analysis":
            print(f"✅ Failure Analysis: Grok 4 analysis triggered for prediction improvement")
            print(f"✅ Mathematical Discovery: Alternative formulas generated for better accuracy")
        elif validation.get("validation_status") == "passed":
            print(f"✅ Prediction Accuracy: Within acceptable threshold - system working correctly")
        else:
            print(f"✅ Prediction Generated: Ready for validation when London data available")
        
        print(f"✅ System Integration: Cross-session prediction workflow operational")
        
        return results
        
    except Exception as e:
        print(f"❌ Cross-session analysis failed: {str(e)}")
        print(f"   Check that Asia session files exist:")
        print(f"   - asia_grokEnhanced_2025_07_22.json")
        print(f"   - HTF_Context_Asia_grokEnhanced_2025_07_22.json")
        print(f"   - FVG_State_Asia_grokEnhanced_2025_07_22.json")
        print(f"   - Liquidity_State_Asia_grokEnhanced_2025_07_22.json")
        return None

def display_system_capabilities():
    """Display cross-session prediction system capabilities"""
    
    print(f"\n🔬 CROSS-SESSION PREDICTION SYSTEM CAPABILITIES")
    print("=" * 55)
    
    print(f"🎯 CORE FUNCTIONALITY:")
    print(f"   • Asia → London session outcome prediction")
    print(f"   • Momentum transfer calculation with temporal decay")
    print(f"   • Liquidity level carryover analysis")
    print(f"   • Session character evolution modeling")
    print(f"   • HTF structure continuity assessment")
    print(f"   • FVG state carryover integration")
    
    print(f"\n🧮 MATHEMATICAL MODELING:")
    print(f"   • Cross-session parameter extraction")
    print(f"   • Temporal decay functions for momentum persistence")
    print(f"   • Volatility transfer coefficients")
    print(f"   • Character-specific multiplier adjustments")
    print(f"   • Distance-based liquidity relevance calculations")
    
    print(f"\n🚨 FAILURE ANALYSIS & DISCOVERY:")
    print(f"   • Automatic error detection (configurable threshold)")
    print(f"   • 5 specialized failure types identification")
    print(f"   • Grok 4 integration for mathematical relationship discovery")
    print(f"   • Alternative formula generation for failed predictions")
    print(f"   • Cross-session specific prompt optimization")
    
    print(f"\n🔄 CONTINUOUS IMPROVEMENT:")
    print(f"   • Every prediction failure becomes learning opportunity")
    print(f"   • Mathematical relationships discovered automatically")
    print(f"   • High-confidence improvements validated and implemented")
    print(f"   • System accuracy improves over time through Grok 4 feedback")
    
    print(f"\n📊 VALIDATION & TESTING:")
    print(f"   • Comprehensive prediction accuracy measurement")
    print(f"   • Multi-dimensional error analysis (close, high, low, character)")
    print(f"   • Confidence scoring based on parameter strength")
    print(f"   • Complete audit trail from Asia data to London prediction")

if __name__ == "__main__":
    print("🌏 Starting Cross-Session Prediction Analysis...")
    
    # Display system capabilities
    display_system_capabilities()
    
    # Run the analysis
    results = run_asia_to_london_analysis()
    
    if results:
        print(f"\n📋 NEXT STEPS:")
        validation = results.get("validation_results", {})
        
        if validation.get("validation_status") == "failed_with_analysis":
            print("1. Review Grok 4 discovered mathematical relationships")
            print("2. Implement high-confidence alternative formulas")
            print("3. Test improved cross-session prediction accuracy")
            print("4. Monitor system learning and adaptation")
        elif validation.get("validation_status") == "passed":
            print("1. Cross-session prediction system working correctly")
            print("2. Monitor continued accuracy on future sessions")
            print("3. Expand to other session pairs (London→NY, etc.)")
            print("4. Refine confidence scoring and error thresholds")
        else:
            print("1. Obtain London session data for validation")
            print("2. Run validation to trigger learning if predictions fail")
            print("3. Use discovered relationships to improve future predictions")
            print("4. Deploy system for real-time cross-session analysis")
    
    print(f"\n🚀 Cross-session prediction system ready for production deployment.")