#!/usr/bin/env python3
"""
Enhanced Validation with Post-Prediction Hooks
Integrates automatic pattern analysis into the validation pipeline
"""

import json
import sys
import os
from datetime import datetime
sys.path.append('.')

from honest_accuracy_framework import HonestAccuracyFramework
from automatic_pattern_analysis import AutomaticPatternAnalyzer
from src.utils import load_json_data, save_json_data

class EnhancedValidationSystem:
    """Validation system with automatic post-prediction pattern analysis hooks"""
    
    def __init__(self, 
                 range_error_threshold: float = 20.0,
                 analysis_threshold: float = 20.0):
        """
        Initialize enhanced validation system
        
        Args:
            range_error_threshold: Threshold for prediction quality assessment
            analysis_threshold: Threshold for triggering automatic analysis
        """
        self.accuracy_framework = HonestAccuracyFramework()
        self.pattern_analyzer = AutomaticPatternAnalyzer(threshold_pct=analysis_threshold)
        self.range_error_threshold = range_error_threshold
        self.analysis_threshold = analysis_threshold
    
    def validate_with_automatic_analysis(self,
                                       prediction_file: str,
                                       actual_file: str,
                                       validation_date: str) -> dict:
        """
        Complete validation with automatic pattern analysis on significant misses
        
        Args:
            prediction_file: Path to prediction results
            actual_file: Path to actual session data
            validation_date: Date in YYYY_MM_DD format
            
        Returns:
            Enhanced validation results with pattern analysis
        """
        
        print("🎯 ENHANCED VALIDATION WITH AUTOMATIC ANALYSIS")
        print("=" * 55)
        print(f"Date: {validation_date}")
        print(f"Analysis Trigger: >{self.analysis_threshold}% range error")
        
        try:
            # Load prediction and actual data
            with open(prediction_file, 'r') as f:
                prediction_data = json.load(f)
            with open(actual_file, 'r') as f:
                actual_data = json.load(f)
            
            # Extract key values
            cross_pred = prediction_data['cross_session_prediction']
            predicted_close = cross_pred['predicted_close']
            
            actual_price_data = actual_data['original_session_data']['price_data']
            actual_close = actual_price_data['close']
            actual_range = actual_price_data['range']
            
            # Step 1: Standard honest accuracy assessment
            print("\n1️⃣ HONEST ACCURACY ASSESSMENT:")
            honest_accuracy = self.accuracy_framework.calculate_honest_accuracy(
                predicted_close=predicted_close,
                actual_close=actual_close,
                actual_range=actual_range,
                session_context={'date': validation_date, 'range': actual_range}
            )
            
            range_error_pct = honest_accuracy['honest_metrics']['range_relative_error_pct']
            print(f"   Range-Relative Error: {range_error_pct:.1f}%")
            print(f"   Quality Assessment: {honest_accuracy['quality_assessment']['grade'].title()}")
            
            # Step 2: POST-PREDICTION HOOK - Automatic analysis trigger
            print(f"\n2️⃣ POST-PREDICTION HOOK CHECK:")
            
            pattern_analysis_triggered = False
            pattern_analysis_results = None
            
            # CRITICAL IMPLEMENTATION: Automatic trigger on significant miss
            if range_error_pct > self.analysis_threshold:
                print(f"   🚨 ERROR > {self.analysis_threshold}% THRESHOLD - TRIGGERING GROK 4 ANALYSIS")
                
                pattern_analysis_triggered = True
                pattern_analysis_results = self.pattern_analyzer.analyze_pattern_miss({
                    "predicted": predicted_close,
                    "actual": actual_close,
                    "error": abs(predicted_close - actual_close),
                    "range": actual_range,
                    "session_data": actual_data['original_session_data'],
                    "focus": "fpfvg_cascade_patterns",
                    "question": "What mathematical relationship predicts this cascade?"
                })
                
            else:
                print(f"   ✅ Error within {self.analysis_threshold}% threshold - No automatic analysis triggered")
            
            # Step 3: Enhanced reporting with pattern insights
            print(f"\n3️⃣ ENHANCED VALIDATION REPORT:")
            enhanced_results = {
                'validation_metadata': {
                    'validation_type': 'enhanced_with_automatic_analysis',
                    'validation_date': validation_date,
                    'timestamp': datetime.now().isoformat(),
                    'analysis_threshold_pct': self.analysis_threshold
                },
                'honest_accuracy_assessment': honest_accuracy,
                'post_prediction_hook': {
                    'triggered': pattern_analysis_triggered,
                    'trigger_reason': f"Range error {range_error_pct:.1f}% > {self.analysis_threshold}% threshold" if pattern_analysis_triggered else "Error within acceptable range",
                    'pattern_analysis_results': pattern_analysis_results
                },
                'prediction_details': {
                    'predicted_close': predicted_close,
                    'actual_close': actual_close,
                    'prediction_error': abs(predicted_close - actual_close),
                    'actual_range': actual_range,
                    'range_error_pct': range_error_pct
                },
                'enhanced_insights': self._generate_enhanced_insights(
                    honest_accuracy, pattern_analysis_results, range_error_pct
                )
            }
            
            # Display enhanced summary
            self._display_enhanced_summary(enhanced_results)
            
            return enhanced_results
            
        except Exception as e:
            print(f"❌ Enhanced validation failed: {str(e)}")
            return {
                'validation_status': 'failed',
                'error': str(e),
                'date': validation_date
            }
    
    def _generate_enhanced_insights(self, 
                                  accuracy_data: dict, 
                                  pattern_results: dict,
                                  range_error_pct: float) -> dict:
        """Generate enhanced insights combining accuracy and pattern analysis"""
        
        insights = {
            'accuracy_insights': {
                'quality_grade': accuracy_data['quality_assessment']['grade'],
                'trading_actionable': accuracy_data['trading_implications']['actionable'],
                'risk_level': accuracy_data['trading_implications']['risk_assessment']
            }
        }
        
        # Add pattern insights if analysis was triggered
        if pattern_results:
            insights['pattern_insights'] = {
                'mathematical_relationships_discovered': len(pattern_results.get('mathematical_relationships', [])),
                'key_patterns': pattern_results.get('discovered_patterns', [])[:2],
                'implementation_priority': pattern_results.get('implementation_priority', 'medium'),
                'grok_analysis_success': pattern_results.get('grok_analysis_success', False)
            }
            
            # Strategic recommendations based on combined analysis  
            if range_error_pct > 40:
                insights['strategic_recommendations'] = [
                    "Implement discovered mathematical relationships immediately",
                    "Focus on FVG cascade timing improvements",
                    "Add threshold-based phase transition detection"
                ]
            elif range_error_pct > 25:
                insights['strategic_recommendations'] = [
                    "Review discovered patterns for implementation",
                    "Test mathematical relationships in next prediction cycle"
                ]
        else:
            insights['pattern_insights'] = {
                'analysis_triggered': False,
                'reason': f'Error {range_error_pct:.1f}% within {self.analysis_threshold}% threshold'
            }
        
        return insights
    
    def _display_enhanced_summary(self, results: dict):
        """Display comprehensive validation summary"""
        
        accuracy = results['honest_accuracy_assessment']
        hook = results['post_prediction_hook'] 
        details = results['prediction_details']
        insights = results['enhanced_insights']
        
        print(f"\n📊 ENHANCED VALIDATION SUMMARY:")
        print(f"   Prediction Error: {details['prediction_error']:.1f} points")
        print(f"   Range-Relative Error: {details['range_error_pct']:.1f}%")
        print(f"   Quality Assessment: {accuracy['quality_assessment']['grade'].title()}")
        print(f"   Trading Actionable: {'✅ YES' if accuracy['trading_implications']['actionable'] else '❌ NO'}")
        
        print(f"\n🪝 POST-PREDICTION HOOK STATUS:")
        print(f"   Automatic Analysis: {'🚨 TRIGGERED' if hook['triggered'] else '✅ Not needed'}")
        if hook['triggered']:
            print(f"   Trigger Reason: {hook['trigger_reason']}")
            pattern_results = hook['pattern_analysis_results']
            if pattern_results:
                relationships = len(pattern_results.get('mathematical_relationships', []))
                patterns = len(pattern_results.get('discovered_patterns', []))
                print(f"   Discoveries: {relationships} relationships, {patterns} patterns")
        
        print(f"\n🚀 STRATEGIC INSIGHTS:")
        if 'strategic_recommendations' in insights:
            for i, rec in enumerate(insights['strategic_recommendations'][:2], 1):
                print(f"   {i}. {rec}")
        else:
            print(f"   • Prediction quality acceptable, no immediate action needed")

def run_enhanced_validation_demo():
    """Demonstrate enhanced validation system"""
    
    # Initialize system
    validator = EnhancedValidationSystem(
        range_error_threshold=20.0,
        analysis_threshold=20.0  # Trigger analysis on >20% range error
    )
    
    # Run enhanced validation on July 23rd data
    results = validator.validate_with_automatic_analysis(
        prediction_file='enhanced_am_lunch_pm_simulation_2025_07_23.json',
        actual_file='ny_pm_grokEnhanced_2025_07_23.json',
        validation_date='2025_07_23'
    )
    
    # Save enhanced results
    with open('enhanced_validation_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Enhanced validation saved to: enhanced_validation_results.json")
    
    return results

def implement_prediction_hook_code():
    """Generate the actual hook code for integration"""
    
    hook_code = '''
# POST-PREDICTION HOOK IMPLEMENTATION
# Add this to your validation pipeline:

def post_prediction_hook(predicted_close, actual_close, session_range, session_data=None):
    """Automatic hook that triggers on significant prediction misses"""
    
    prediction_error = abs(predicted_close - actual_close)
    error_threshold = 0.2 * session_range  # 20% of session range
    
    if prediction_error > error_threshold:
        print(f"🚨 Significant prediction miss detected: {prediction_error:.1f} points")
        
        # Trigger automatic Grok 4 analysis
        from automatic_pattern_analysis import AutomaticPatternAnalyzer
        analyzer = AutomaticPatternAnalyzer()
        
        analyzer.analyze_pattern_miss({
            "predicted": predicted_close,
            "actual": actual_close,
            "error": prediction_error,
            "range": session_range,
            "session_data": session_data,
            "focus": "fpfvg_cascade_patterns",
            "question": "What mathematical relationship predicts this cascade?"
        })
        
        return True  # Analysis triggered
    
    return False  # No analysis needed

# USAGE EXAMPLE:
# After any prediction validation:
triggered = post_prediction_hook(23285.73, 23350.50, 162.5, session_data)
'''
    
    print("🔧 POST-PREDICTION HOOK CODE:")
    print("=" * 35)
    print(hook_code)
    
    # Save hook code
    with open('post_prediction_hook.py', 'w') as f:
        f.write(f"#!/usr/bin/env python3\n{hook_code}")
    
    print("💾 Hook code saved to: post_prediction_hook.py")

def main():
    """Main execution"""
    print("🚀 ENHANCED VALIDATION SYSTEM WITH AUTOMATIC HOOKS")
    print("=" * 60)
    
    # Demo 1: Enhanced validation with automatic analysis
    print("\n1️⃣ RUNNING ENHANCED VALIDATION DEMO:")
    run_enhanced_validation_demo()
    
    # Demo 2: Generate hook implementation code
    print("\n2️⃣ GENERATING POST-PREDICTION HOOK CODE:")
    implement_prediction_hook_code()
    
    print(f"\n🎯 SYSTEM ENHANCEMENTS COMPLETE")
    print("=" * 35)
    print("✅ Automatic Grok 4 analysis on >20% range error")
    print("✅ Post-prediction hooks implemented")
    print("✅ FVG cascade pattern discovery active")
    print("✅ Mathematical relationship extraction working")
    print("✅ Enhanced validation pipeline operational")

if __name__ == "__main__":
    main()