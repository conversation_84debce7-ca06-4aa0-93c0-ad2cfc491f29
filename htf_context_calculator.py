#!/usr/bin/env python3
"""
HTF Context Weight Calculator
Implements γ_HTF(t) calculation for preprocessing weight injection
Based on Opus specifications: γ_HTF(t) = α * exp(-β * hours_since_sweep) * liquidity_imbalance_ratio
"""

import json
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple
from pathlib import Path

class HTFContextCalculator:
    """
    Calculates HTF context weights for session preprocessing
    Implements the mathematical framework for λ_total(t) = λ_session(t) × (1 + γ_HTF(t))
    """
    
    def __init__(self):
        # HTF Parameters (from Opus specifications)
        self.alpha = 0.8  # Excitation coefficient
        self.beta = 0.005  # Decay rate (slower for HTF)
        
        # Weight scaling (Opus: 0.5-1.5 range to avoid extreme distortions)
        self.min_weight = 0.5
        self.max_weight = 1.5
        self.base_weight = 1.0
        
    def calculate_htf_context_weight(self, hours_since_sweep: float, 
                                   liquidity_imbalance_ratio: float) -> float:
        """
        Calculate HTF context weight using the mathematical framework
        γ_HTF(t) = α * exp(-β * hours_since_sweep) * liquidity_imbalance_ratio
        Range: 0.5-1.5 to avoid extreme distortions
        """
        # Calculate base gamma value
        gamma = self.alpha * np.exp(-self.beta * hours_since_sweep) * liquidity_imbalance_ratio
        
        # Convert to preprocessing weight: 1 + γ_HTF(t) clamped to safe range
        weight = self.base_weight + gamma
        
        # Clamp to safe range
        weight = np.clip(weight, self.min_weight, self.max_weight)
        
        return weight
    
    def calculate_liquidity_imbalance_ratio(self, session_data: Dict) -> float:
        """
        Calculate liquidity imbalance ratio from session data
        liquidity_imbalance_ratio = untaken_liquidity_above / untaken_liquidity_below
        """
        untaken_liquidity_above = 0.0
        untaken_liquidity_below = 0.0
        current_price = self._get_current_price(session_data)
        
        # Extract untaken liquidity from various sources
        liquidity_levels = self._extract_liquidity_levels(session_data)
        
        for level_data in liquidity_levels:
            level = level_data.get('level', 0)
            weight = level_data.get('weight', 1.0)
            significance = level_data.get('significance', 1.0)
            
            # Apply significance weighting
            weighted_liquidity = weight * significance
            
            if level > current_price:
                untaken_liquidity_above += weighted_liquidity
            else:
                untaken_liquidity_below += weighted_liquidity
        
        # Calculate ratio, avoiding division by zero
        if untaken_liquidity_below > 0:
            ratio = untaken_liquidity_above / untaken_liquidity_below
        else:
            ratio = 1.0 if untaken_liquidity_above > 0 else 0.5
        
        # Normalize ratio to reasonable range (0.1 - 5.0)
        ratio = np.clip(ratio, 0.1, 5.0)
        
        return ratio
    
    def _get_current_price(self, session_data: Dict) -> float:
        """Extract current/close price from session data"""
        if 'price_data' in session_data:
            price_data = session_data['price_data']
            return price_data.get('close', price_data.get('current', price_data.get('high', 0)))
        return 0.0
    
    def _extract_liquidity_levels(self, session_data: Dict) -> List[Dict]:
        """Extract liquidity levels from various session data formats"""
        liquidity_levels = []
        
        # Try different locations for liquidity data
        if 'liquidity_analysis' in session_data:
            liquidity_analysis = session_data['liquidity_analysis']
            if 'untaken_liquidity' in liquidity_analysis:
                for level in liquidity_analysis['untaken_liquidity']:
                    liquidity_levels.append({
                        'level': level.get('level', 0),
                        'weight': level.get('weight', 1.0),
                        'significance': self._map_significance(level.get('significance', 'medium'))
                    })
        
        # Check active_structures for structure-based liquidity
        if 'active_structures' in session_data:
            for structure in session_data['active_structures']:
                liquidity_levels.append({
                    'level': structure.get('level', 0),
                    'weight': structure.get('strength', 0.5) * 2.0,  # Convert strength to liquidity weight
                    'significance': 1.0
                })
        
        # Check HTF context for HTF liquidity levels
        if 'htf_context' in session_data and 'active_structures' in session_data['htf_context']:
            for structure in session_data['htf_context']['active_structures']:
                liquidity_levels.append({
                    'level': structure.get('level', 0),
                    'weight': structure.get('strength', 0.5) * 1.5,  # HTF structures have higher weight
                    'significance': 1.2  # HTF significance boost
                })
        
        # Fallback: Create basic liquidity zones from price data
        if not liquidity_levels and 'price_data' in session_data:
            price_data = session_data['price_data']
            high = price_data.get('high', 0)
            low = price_data.get('low', 0)
            
            if high > 0 and low > 0:
                liquidity_levels.extend([
                    {'level': high, 'weight': 1.5, 'significance': 1.0},  # Session high liquidity
                    {'level': low, 'weight': 1.5, 'significance': 1.0}    # Session low liquidity
                ])
        
        return liquidity_levels
    
    def _map_significance(self, significance_str: str) -> float:
        """Map significance strings to numerical values"""
        mapping = {
            'high': 1.5,
            'medium': 1.0,
            'low': 0.5,
            'extreme': 2.0
        }
        return mapping.get(significance_str.lower(), 1.0)
    
    def calculate_hours_since_sweep(self, current_session_date: str, 
                                  htf_events: List[Dict]) -> float:
        """
        Calculate hours since last HTF sweep event
        """
        try:
            current_dt = datetime.fromisoformat(current_session_date.replace('_', '-'))
        except:
            # Fallback parsing
            from dateutil import parser
            current_dt = parser.parse(current_session_date.replace('_', '-'))
        
        # Find most recent sweep event
        most_recent_sweep = None
        min_hours_diff = float('inf')
        
        for event in htf_events:
            if event.get('type') in ['sweep', 'cascade']:
                try:
                    event_dt = datetime.fromisoformat(event['date'].replace('_', '-'))
                    hours_diff = (current_dt - event_dt).total_seconds() / 3600
                    
                    if 0 <= hours_diff < min_hours_diff:
                        min_hours_diff = hours_diff
                        most_recent_sweep = event
                except:
                    continue
        
        if most_recent_sweep:
            return min_hours_diff
        else:
            # No recent sweep found - use default decay period
            return 48.0  # 2 days default
    
    def generate_htf_context_for_session(self, session_file: str, 
                                       htf_events: List[Dict]) -> Dict:
        """
        Generate HTF context data for a specific session
        Returns HTF_Context enhancement data
        """
        try:
            # Load session data
            with open(session_file, 'r') as f:
                session_data = json.load(f)
            
            # Extract session date from filename or data
            session_date = self._extract_session_date(session_file, session_data)
            
            # Calculate components
            hours_since_sweep = self.calculate_hours_since_sweep(session_date, htf_events)
            liquidity_imbalance = self.calculate_liquidity_imbalance_ratio(session_data)
            htf_context_weight = self.calculate_htf_context_weight(hours_since_sweep, liquidity_imbalance)
            
            # Determine weekly phase
            weekly_phase = self._determine_weekly_phase(session_date, htf_events)
            
            # Calculate next expected event
            next_expected = self._predict_next_event(session_date, htf_events, weekly_phase)
            
            # Generate HTF context structure (as specified by Opus)
            htf_context = {
                "weekly_phase": weekly_phase,
                "hours_since_htf_event": hours_since_sweep,
                "gamma_htf_current": htf_context_weight - 1.0,  # Pure gamma value
                "htf_context_weight": htf_context_weight,  # Full preprocessing weight
                "next_expected_event": next_expected,
                "liquidity_imbalance": liquidity_imbalance,
                "calculation_metadata": {
                    "session_file": session_file,
                    "session_date": session_date,
                    "alpha": self.alpha,
                    "beta": self.beta,
                    "calculation_time": datetime.now().isoformat()
                }
            }
            
            return htf_context
            
        except Exception as e:
            print(f"Error generating HTF context for {session_file}: {e}")
            return self._generate_fallback_context(session_file)
    
    def _extract_session_date(self, session_file: str, session_data: Dict) -> str:
        """Extract session date from file path or data"""
        import re
        
        # Try to extract from filename
        filename = Path(session_file).name
        date_pattern = r'(\d{4}[-_]\d{2}[-_]\d{2})'
        match = re.search(date_pattern, filename)
        
        if match:
            return match.group(1).replace('_', '-')
        
        # Try to extract from session data
        if 'session_metadata' in session_data:
            metadata = session_data['session_metadata']
            if 'date' in metadata:
                return metadata['date']
            elif 'session_date' in metadata:
                return metadata['session_date']
        
        # Fallback to current date
        return datetime.now().strftime('%Y-%m-%d')
    
    def _determine_weekly_phase(self, session_date: str, htf_events: List[Dict]) -> str:
        """
        Determine current weekly phase based on recent HTF events
        Returns: trap|sweep|cascade|neutral
        """
        try:
            current_dt = datetime.fromisoformat(session_date)
            weekday = current_dt.weekday()  # 0=Monday, 4=Friday
            
            # Look for recent events (last 3 days)
            recent_events = []
            for event in htf_events:
                try:
                    event_dt = datetime.fromisoformat(event['date'].replace('_', '-'))
                    days_diff = (current_dt - event_dt).days
                    if 0 <= days_diff <= 3:
                        recent_events.append(event)
                except:
                    continue
            
            # Classify based on weekday and recent events
            if weekday == 4:  # Friday
                return "trap"  # Friday trap formation
            elif weekday == 0:  # Monday
                trap_events = [e for e in recent_events if e.get('type') == 'trap']
                if trap_events:
                    return "sweep"  # Monday sweep after Friday trap
            elif weekday == 1:  # Tuesday  
                sweep_events = [e for e in recent_events if e.get('type') == 'sweep']
                if sweep_events:
                    return "cascade"  # Tuesday cascade after Monday sweep
            
            return "neutral"
            
        except:
            return "neutral"
    
    def _predict_next_event(self, session_date: str, htf_events: List[Dict], 
                          weekly_phase: str) -> str:
        """Predict next expected HTF event based on current phase"""
        phase_transitions = {
            "trap": "sweep",
            "sweep": "cascade", 
            "cascade": "neutral",
            "neutral": "trap"
        }
        return phase_transitions.get(weekly_phase, "neutral")
    
    def _generate_fallback_context(self, session_file: str) -> Dict:
        """Generate fallback HTF context when calculation fails"""
        return {
            "weekly_phase": "neutral",
            "hours_since_htf_event": 24.0,
            "gamma_htf_current": 0.0,
            "htf_context_weight": 1.0,  # Neutral weight
            "next_expected_event": "neutral",
            "liquidity_imbalance": 1.0,
            "calculation_metadata": {
                "session_file": session_file,
                "fallback": True,
                "calculation_time": datetime.now().isoformat()
            }
        }

def main():
    """Test the HTF Context Calculator"""
    print("🧮 HTF Context Weight Calculator")
    print("=" * 50)
    
    calculator = HTFContextCalculator()
    
    # Test calculations
    print("\n🧪 Testing HTF weight calculations:")
    
    test_cases = [
        (2.0, 0.8),   # Recent sweep, balanced liquidity
        (12.0, 1.5),  # Half day since sweep, imbalanced above
        (48.0, 0.5),  # 2 days since sweep, imbalanced below
        (96.0, 1.0),  # 4 days since sweep, balanced
    ]
    
    for hours, ratio in test_cases:
        weight = calculator.calculate_htf_context_weight(hours, ratio)
        gamma = weight - 1.0
        print(f"Hours: {hours:4.1f}, Ratio: {ratio:3.1f} → Weight: {weight:.3f}, γ_HTF: {gamma:+.3f}")
    
    # Test with actual session file if available
    test_files = [
        "data/enhanced/grok_enhanced/ASIA_grokEnhanced_2025_07_25.json",
        "HTF_Context_Lunch_grokEnhanced_2025-07-25.json",
        "LUNCH_Lvl-1_2025_07_25.json"
    ]
    
    for test_file in test_files:
        if Path(test_file).exists():
            print(f"\n📊 Testing with {test_file}:")
            
            # Generate mock HTF events for testing
            mock_htf_events = [
                {"date": "2025-07-24", "type": "sweep", "magnitude": 25.5},
                {"date": "2025-07-23", "type": "trap", "magnitude": 15.2}
            ]
            
            context = calculator.generate_htf_context_for_session(test_file, mock_htf_events)
            
            print(f"  Weekly Phase: {context['weekly_phase']}")
            print(f"  HTF Weight: {context['htf_context_weight']:.3f}")
            print(f"  Liquidity Imbalance: {context['liquidity_imbalance']:.3f}")
            print(f"  Hours Since Event: {context['hours_since_htf_event']:.1f}")
            break
    
    print(f"\n✅ HTF Context Calculator ready for integration")

if __name__ == "__main__":
    main()