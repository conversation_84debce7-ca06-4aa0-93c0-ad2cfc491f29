{"foundation_calculations": {"extracted_values": {"rate_per_minute": 0.10472972972972973, "session_range": 31.0, "duration_minutes": 296, "complexity_score": 1.0, "session_type": "Asia", "largest_single_move": 50.0}, "session_type": "Asia", "complexity_score": 1.0}, "hybrid_volume": {"v_s": 10.472972972972974, "alpha_t": 0.20945945945945948, "volume_multiplier": 1.0, "source_data": {"rate_per_minute": 0.10472972972972973, "complexity_factor": 1.0}}, "time_dilation_base": {"gamma_base": 0.9144085092485275, "time_factor": 0.9866666666666667, "dilation_factor": 0.9022163957918804, "range_normalized": 0.31}, "parameters_validated": {"alpha_t": 0.20945945945945948, "gamma_base": 0.9144085092485275, "d_htf": 0.05745945362169801, "complexity_validation": {"alpha_range_valid": true, "gamma_range_valid": "True", "complexity_range_valid": true, "convergence_achieved": true}, "parameter_summary": {"foundation_strength": 0.5619339843539934, "computational_stability": 1.0}}, "energy_structure_calculations": {"session_context": "ranging_with_multiple_reversals_and_redeliveries", "foundation_inputs": {"alpha_t": 0.20945945945945948, "gamma_base": 0.9144085092485275, "d_htf": 0.05745945362169801}}, "energy_accumulation": {"energy_rate": 0.5256739041259096, "total_accumulated": 157.7021712377729, "accumulation_phase": "stable", "efficiency_factor": 0.2628369520629548, "energy_metrics": {"alpha_contribution": 0.2513513513513514, "gamma_contribution": 0.27432255277455825, "rate_normalized": 0.17522463470863656}}, "gradient_dynamics": {"intensity_coefficient": 0.19153151207232672, "direction_factor": 1.0, "stability_index": 0.6096056728323516, "momentum_transfer": 0.15322520965786138, "gradient_metrics": {"alpha_gamma_product": 0.19153151207232672, "stability_normalized": 0.6096056728323516, "momentum_strength": 0.15322520965786138}}, "structural_integrity": 0.5676397884791307, "advanced_dynamics": {"foundation_inputs": {"gamma_base": 0.9144085092485275, "energy_rate": 0.5256739041259096}, "structure_inputs": {"structural_integrity": 0.5676397884791307, "stability_index": 0.6096056728323516}}, "temporal_momentum": {"momentum_strength": 0.2403403455113132, "momentum_direction": "bearish", "decay_coefficient": 0.8062303251030376, "persistence_factor": 0.09576575603616336, "momentum_metrics": {"strength_normalized": 0.08011344850377107, "directional_bias": 0.9}}, "consolidation_analysis": {"consolidation_strength": 0.5886227306557411, "breakout_probability": 0.4113772693442589, "consolidation_duration": 60, "consolidation_metrics": {"strength_ratio": 0.5886227306557411, "breakout_likelihood": 0.4113772693442589, "phase_stability": 0.34603643518223415}}, "frequency_analysis": {"primary_frequency": "high_frequency", "harmonic_resonance": 0.3291018154754071, "frequency_factor": 1.6444444444444444, "frequency_metrics": {"breakout_timing": 0.6764870651438923, "consolidation_persistence": 0.3579462551284912}}, "integration_synthesis": {"all_unit_inputs": {"alpha_t": 0.20945945945945948, "energy_rate": 0.5256739041259096, "structural_integrity": 0.5676397884791307, "momentum_strength": 0.2403403455113132}}, "validation_results": {"integration_score": 0.32515790303222747, "consistency_check": "True", "convergence_achieved": "False", "error_margins": {"alpha_deviation": 0.7905405405405406, "energy_deviation": 0.6495507305827269, "momentum_deviation": 0.7997163787405723}, "validation_metrics": {"alpha_range_valid": true, "energy_range_valid": "True", "momentum_range_valid": "True", "structure_range_valid": "True", "overall_validity": "True"}}, "synthesis_results": {"final_confidence": 0.18457256329953128, "recommendations": ["Increase energy accumulation for better integration", "Overall system requires optimization"], "synthesis_metrics": {"confidence_level": "low", "system_stability": 0.5676397884791307, "integration_quality": 0.32515790303222747}}, "quality_metrics": {"processing_quality": "needs_improvement", "data_consistency": "high", "session_compatibility": "unknown", "overall_score": 18.45725632995313, "quality_summary": {"mathematical_soundness": "validated", "computational_efficiency": "optimized", "result_reliability": "needs_improvement"}}, "hawkes_cascade_prediction": {"cascade_time": 2, "confidence": 0.95, "methodology": "hawkes_process_with_dynamic_synthetic_volume", "iterations_used": 3}, "pipeline_metadata": {"total_processing_time_ms": 0.11706352233886719, "units_completed": ["A", "B", "C", "D"], "local_computation": true, "jit_optimized": true, "performance_vs_grok": "0.1ms vs ~67,500ms (576610x faster)", "final_confidence": 0.18457256329953128, "processing_quality": "needs_improvement", "computation_method": "local_numpy", "api_calls_made": 0, "api_calls_avoided": 4, "estimated_time_saved_ms": 67499.88293647766, "performance_class": "excellent"}, "unit_status": "completed", "local_computation": true, "grok_compatible": true}