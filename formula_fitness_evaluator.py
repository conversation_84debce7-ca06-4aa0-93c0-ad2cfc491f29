#!/usr/bin/env python3
"""
Formula Fitness Evaluator
Evaluates timing formula fitness using real historical cascade data.
Integrates with existing event timing validation system.
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import numpy as np

try:
    from event_timing_logger import EventTimingLogger, TimingAccuracy
    from cascade_timing_test_july23 import load_july23_data, extract_actual_cascade_timing
except ImportError as e:
    print(f"⚠️ Import warning: {e}")

@dataclass
class FormulaTestCase:
    """Historical test case for formula evaluation"""
    test_name: str
    session_data: Dict
    tracker_context: Dict
    actual_events: List[Dict]
    expected_timing: datetime
    context_description: str

class FormulaFitnessEvaluator:
    """Evaluates timing formula performance against historical data"""
    
    def __init__(self):
        self.test_cases = []
        self.timing_logger = None
        self.load_historical_test_cases()
        
        # Initialize timing logger for fitness tracking
        try:
            self.timing_logger = EventTimingLogger("formula_evolution_accuracy.json")
            print("✅ Connected to timing accuracy logger")
        except Exception as e:
            print(f"⚠️ Could not initialize timing logger: {e}")
    
    def load_historical_test_cases(self):
        """Load historical test cases for formula evaluation"""
        
        # July 23rd cascade test case (proven validation)
        july23_test = self._create_july23_test_case()
        if july23_test:
            self.test_cases.append(july23_test)
        
        # Additional test cases can be added here
        # self.test_cases.extend(self._create_additional_test_cases())
        
        print(f"📊 Loaded {len(self.test_cases)} historical test cases")
    
    def _create_july23_test_case(self) -> Optional[FormulaTestCase]:
        """Create July 23rd cascade test case"""
        
        try:
            # Load July 23rd data if available
            lunch_file = "lunch_grokEnhanced_2025_07_23.json"
            pm_file = "ny_pm_grokEnhanced_2025_07_23.json"
            
            if not (os.path.exists(lunch_file) and os.path.exists(pm_file)):
                print(f"⚠️ July 23rd test files not found, creating synthetic test case")
                return self._create_synthetic_july23_test()
            
            with open(lunch_file, 'r') as f:
                lunch_data = json.load(f)
            with open(pm_file, 'r') as f:
                pm_data = json.load(f)
            
            # Extract tracker context
            tracker_context = lunch_data.get('tracker_context', {})
            
            # Extract actual cascade events
            actual_events = []
            price_movements = pm_data.get('original_session_data', {}).get('price_movements', [])
            
            for movement in price_movements:
                if movement.get('action') == 'break':
                    actual_events.append({
                        'time': movement['timestamp'],
                        'type': 'cascade',
                        'context': movement['context']
                    })
            
            # Known cascade timing: 13:45
            expected_timing = datetime(2025, 7, 23, 13, 45)
            
            return FormulaTestCase(
                test_name="july23_cascade_validation",
                session_data=lunch_data,
                tracker_context=tracker_context,
                actual_events=actual_events,
                expected_timing=expected_timing,
                context_description="July 23rd 13:45 cascade - proven validation case"
            )
            
        except Exception as e:
            print(f"⚠️ Could not load July 23rd data: {e}")
            return self._create_synthetic_july23_test()
    
    def _create_synthetic_july23_test(self) -> FormulaTestCase:
        """Create synthetic July 23rd test case with known parameters"""
        
        # Synthetic tracker context based on known July 23rd parameters
        tracker_context = {
            "t_memory": 15.0,
            "active_structures": [
                {"level": 23367.0, "strength": 0.8},
                {"level": 23350.5, "strength": 0.7},
                {"level": 23345.25, "strength": 0.9}
            ],
            "untaken_liquidity": [
                {"level": 23424.75, "weight": 2.1},
                {"level": 23328.5, "weight": 1.7}
            ],
            "liquidity_gradient": {
                "gradient_strength": 0.017,
                "weighted_pull": 0.178
            }
        }
        
        # Synthetic session data
        session_data = {
            "session_metadata": {
                "session_type": "lunch",
                "date": "2025-07-23"
            },
            "original_session_data": {
                "session_character": "expansion_consolidation_retracement"
            },
            "tracker_context": tracker_context
        }
        
        # Known actual events
        actual_events = [
            {
                "time": "13:45:00",
                "type": "cascade",
                "context": "lunch_session_high_sweep"
            }
        ]
        
        expected_timing = datetime(2025, 7, 23, 13, 45)
        
        return FormulaTestCase(
            test_name="synthetic_july23_cascade",
            session_data=session_data,
            tracker_context=tracker_context,
            actual_events=actual_events,
            expected_timing=expected_timing,
            context_description="Synthetic July 23rd cascade with known parameters"
        )
    
    def evaluate_formula_fitness(self, formula_code: str, formula_id: str) -> Dict:
        """Evaluate formula fitness across all test cases"""
        
        print(f"🧪 Evaluating formula: {formula_id}")
        
        test_results = []
        
        for test_case in self.test_cases:
            result = self._test_formula_on_case(formula_code, formula_id, test_case)
            test_results.append(result)
        
        # Calculate overall fitness metrics
        fitness_metrics = self._calculate_fitness_metrics(test_results)
        
        # Log results if timing logger available
        if self.timing_logger:
            self._log_formula_results(formula_id, test_results)
        
        return {
            "formula_id": formula_id,
            "formula_code": formula_code,
            "test_results": test_results,
            "fitness_metrics": fitness_metrics,
            "evaluation_timestamp": datetime.now().isoformat()
        }
    
    def _test_formula_on_case(self, formula_code: str, formula_id: str, test_case: FormulaTestCase) -> Dict:
        """Test formula on a single test case"""
        
        try:
            # Extract parameters from test case
            tracker = test_case.tracker_context
            t_memory = tracker.get("t_memory", 10.0)
            
            # Calculate derived parameters
            fvg_density = len(tracker.get("active_structures", [])) / 10.0  # Normalize
            volatility_index = tracker.get("liquidity_gradient", {}).get("gradient_strength", 0.01) * 100
            options_proximity = 0.75  # Default
            session_character_weight = 0.7 if "consolidation" in test_case.session_data.get("original_session_data", {}).get("session_character", "") else 1.0
            fvg_cluster_density = fvg_density
            liquidity_magnetic_pull = tracker.get("liquidity_gradient", {}).get("weighted_pull", 0.1)
            t_memory_acceleration = t_memory / 10.0
            consolidation_strength = 0.4 if "consolidation" in test_case.session_data.get("original_session_data", {}).get("session_character", "") else 0.1
            expansion_momentum = 1.3 if "expansion" in test_case.session_data.get("original_session_data", {}).get("session_character", "") else 0.8
            
            # Execute formula
            local_vars = {
                "t_memory": t_memory,
                "fvg_density": fvg_density,
                "volatility_index": volatility_index,
                "options_proximity": options_proximity,
                "session_character_weight": session_character_weight,
                "fvg_cluster_density": fvg_cluster_density,
                "liquidity_magnetic_pull": liquidity_magnetic_pull,
                "t_memory_acceleration": t_memory_acceleration,
                "consolidation_strength": consolidation_strength,
                "expansion_momentum": expansion_momentum,
                "math": __import__("math"),
                "np": np
            }
            
            exec(formula_code, {}, local_vars)
            formula_result = local_vars.get("result", 0.0)
            
            # Convert formula result to timing prediction
            base_time = datetime(2025, 7, 23, 13, 30)  # Session start
            timing_offset_minutes = min(60, max(0, abs(formula_result) * 1.5))  # Scale and bound
            predicted_timing = base_time + timedelta(minutes=timing_offset_minutes)
            
            # Calculate timing accuracy
            actual_timing = test_case.expected_timing
            timing_error_minutes = abs((predicted_timing - actual_timing).total_seconds() / 60)
            success = timing_error_minutes <= 5.0
            
            return {
                "test_case": test_case.test_name,
                "predicted_timing": predicted_timing.strftime("%H:%M:%S"),
                "actual_timing": actual_timing.strftime("%H:%M:%S"),
                "timing_error_minutes": timing_error_minutes,
                "success": success,
                "formula_result": formula_result,
                "parameters_used": {
                    "t_memory": t_memory,
                    "fvg_density": fvg_density,
                    "volatility_index": volatility_index
                },
                "context": test_case.context_description
            }
            
        except Exception as e:
            # Formula execution failed
            return {
                "test_case": test_case.test_name,
                "predicted_timing": "FAILED",
                "actual_timing": test_case.expected_timing.strftime("%H:%M:%S"),
                "timing_error_minutes": 999.0,
                "success": False,
                "formula_result": None,
                "error": str(e),
                "context": test_case.context_description
            }
    
    def _calculate_fitness_metrics(self, test_results: List[Dict]) -> Dict:
        """Calculate comprehensive fitness metrics"""
        
        if not test_results:
            return {"fitness_score": 0.0}
        
        # Extract timing errors and success rates
        timing_errors = [r["timing_error_minutes"] for r in test_results if isinstance(r["timing_error_minutes"], (int, float))]
        successes = [r["success"] for r in test_results]
        
        if not timing_errors:
            return {"fitness_score": 0.0}
        
        # Calculate metrics
        mean_timing_error = np.mean(timing_errors)
        median_timing_error = np.median(timing_errors)
        success_rate = np.mean(successes)
        consistency_score = 1.0 - (np.std(timing_errors) / (np.mean(timing_errors) + 1e-6))
        
        # Calculate weighted fitness score
        # Lower timing error = higher fitness
        timing_fitness = max(0, 1.0 - (mean_timing_error / 60.0))  # Normalize to 0-1
        success_fitness = success_rate
        consistency_fitness = max(0, consistency_score)
        
        # Weighted combination (same as FormulaEvolution)
        fitness_score = (
            timing_fitness * 0.6 +
            success_fitness * 0.2 +
            consistency_fitness * 0.2
        )
        
        return {
            "fitness_score": fitness_score,
            "mean_timing_error_minutes": mean_timing_error,
            "median_timing_error_minutes": median_timing_error,
            "success_rate": success_rate,
            "consistency_score": consistency_score,
            "total_tests": len(test_results),
            "successful_tests": sum(successes),
            "quality_assessment": self._assess_quality(mean_timing_error, success_rate)
        }
    
    def _assess_quality(self, mean_error: float, success_rate: float) -> str:
        """Assess formula quality based on timing performance"""
        
        if success_rate >= 0.8 and mean_error <= 3.0:
            return "Excellent - High timing accuracy"
        elif success_rate >= 0.6 and mean_error <= 5.0:
            return "Good - Acceptable timing accuracy"
        elif success_rate >= 0.4 and mean_error <= 8.0:
            return "Moderate - Needs improvement"
        else:
            return "Poor - Significant timing errors"
    
    def _log_formula_results(self, formula_id: str, test_results: List[Dict]):
        """Log formula results to timing accuracy logger"""
        
        if not self.timing_logger:
            return
        
        for result in test_results:
            if result["success"] and "FAILED" not in result["predicted_timing"]:
                # Log successful prediction for accuracy tracking
                try:
                    predicted_time = datetime.strptime(f"2025-07-23 {result['predicted_timing']}", "%Y-%m-%d %H:%M:%S")
                    actual_time = datetime.strptime(f"2025-07-23 {result['actual_timing']}", "%Y-%m-%d %H:%M:%S")
                    
                    # This would integrate with the timing logger
                    # For now, just track in our own system
                    pass
                    
                except Exception as e:
                    print(f"⚠️ Could not log to timing logger: {e}")
    
    def run_comparative_analysis(self, formulas: List[Tuple[str, str]]) -> Dict:
        """Run comparative analysis across multiple formulas"""
        
        print(f"🔬 Running comparative analysis on {len(formulas)} formulas")
        
        all_results = []
        
        for formula_code, formula_id in formulas:
            result = self.evaluate_formula_fitness(formula_code, formula_id)
            all_results.append(result)
        
        # Rank formulas by fitness
        all_results.sort(key=lambda x: x["fitness_metrics"]["fitness_score"], reverse=True)
        
        # Create comparative analysis
        comparison = {
            "analysis_metadata": {
                "total_formulas_tested": len(formulas),
                "test_cases_used": len(self.test_cases),
                "analysis_timestamp": datetime.now().isoformat()
            },
            "formula_rankings": [
                {
                    "rank": i + 1,
                    "formula_id": result["formula_id"],
                    "fitness_score": result["fitness_metrics"]["fitness_score"],
                    "mean_timing_error": result["fitness_metrics"]["mean_timing_error_minutes"],
                    "success_rate": result["fitness_metrics"]["success_rate"],
                    "quality": result["fitness_metrics"]["quality_assessment"]
                }
                for i, result in enumerate(all_results)
            ],
            "best_formula": {
                "formula_id": all_results[0]["formula_id"],
                "formula_code": all_results[0]["formula_code"],
                "fitness_metrics": all_results[0]["fitness_metrics"]
            } if all_results else None,
            "detailed_results": all_results
        }
        
        print(f"🏆 Best formula: {comparison['best_formula']['formula_id'] if comparison['best_formula'] else 'None'}")
        
        return comparison

def main():
    """Demonstrate formula fitness evaluator"""
    
    print("🧪 FORMULA FITNESS EVALUATOR - Timing Accuracy Testing")
    print("=" * 60)
    
    evaluator = FormulaFitnessEvaluator()
    
    # Test formulas to compare
    test_formulas = [
        ("result = t_memory**1.5 * fvg_density", "base_formula"),
        ("result = t_memory**1.7 * fvg_density * volatility_index", "enhanced_v1"),
        ("result = t_memory**2.0 * fvg_density + consolidation_strength", "enhanced_v2"),
        ("result = t_memory**1.3 * fvg_density * math.exp(options_proximity * 0.1)", "exponential_v1")
    ]
    
    # Run comparative analysis
    comparison = evaluator.run_comparative_analysis(test_formulas)
    
    # Save results
    output_file = f"formula_fitness_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(comparison, f, indent=2)
    
    print(f"\n📊 COMPARATIVE ANALYSIS RESULTS:")
    for ranking in comparison["formula_rankings"]:
        print(f"   {ranking['rank']}. {ranking['formula_id']}: "
              f"fitness={ranking['fitness_score']:.3f}, "
              f"error={ranking['mean_timing_error']:.1f}min, "
              f"success={ranking['success_rate']:.1%}")
    
    print(f"\n💾 Detailed results saved to: {output_file}")
    
    if comparison["best_formula"]:
        best = comparison["best_formula"]
        print(f"\n🎯 BEST FORMULA IDENTIFIED:")
        print(f"   Formula: {best['formula_code']}")
        print(f"   Fitness: {best['fitness_metrics']['fitness_score']:.3f}")
        print(f"   Timing Error: {best['fitness_metrics']['mean_timing_error_minutes']:.1f} minutes")
        print(f"   Quality: {best['fitness_metrics']['quality_assessment']}")

if __name__ == "__main__":
    main()