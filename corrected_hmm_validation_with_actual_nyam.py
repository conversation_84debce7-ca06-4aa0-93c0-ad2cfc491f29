#!/usr/bin/env python3
"""
Corrected HMM Monte Carlo Validation with Actual NYAM Data
Properly extracts timing events from the actual NYAM Lvl-1 session file
instead of using hardcoded values from previous validation.
"""

import json
from datetime import datetime, time
from typing import Dict, Any, <PERSON><PERSON>
import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data

def _timestamp_to_minutes(timestamp_str: str, session_start: str = "09:30:00") -> float:
    """Convert timestamp to minutes from session start (CORRECTED VERSION)"""
    
    # Clean session_start time (remove timezone if present)
    clean_session_start = session_start.split(' ')[0]  # Remove ' ET' if present
    
    # Parse the timestamps
    event_time = datetime.strptime(timestamp_str, "%H:%M:%S").time()
    start_time = datetime.strptime(clean_session_start, "%H:%M:%S").time()
    
    # Convert to total minutes from midnight
    event_total_minutes = event_time.hour * 60 + event_time.minute + event_time.second / 60.0
    start_total_minutes = start_time.hour * 60 + start_time.minute + start_time.second / 60.0
    
    # Return minutes from session start (NOT from midnight)
    return event_total_minutes - start_total_minutes

def extract_actual_cascade_and_expansion_timing(nyam_data: Dict[str, Any]) -> Dict[str, Any]:
    """Extract actual cascade and expansion timing from NYAM Lvl-1 data"""
    
    print("🔍 Analyzing NYAM Lvl-1 Session Data for Actual Events...")
    
    # Session start time
    session_start = nyam_data['session_metadata']['start_time']
    print(f"   Session Start: {session_start}")
    
    # Analyze price movements for significant events
    price_movements = nyam_data.get('price_movements', [])
    cascade_events = []
    expansion_events = []
    
    print(f"\n📊 Price Movement Analysis:")
    for movement in price_movements:
        timestamp = movement['timestamp']
        minutes_from_start = _timestamp_to_minutes(timestamp, session_start)
        context = movement['context']
        
        print(f"   {timestamp} ({minutes_from_start:4.1f} min): {context}")
        
        # Identify cascade events (significant breaks/liquidity sweeps)
        if any(keyword in context.lower() for keyword in ['break', 'taken', 'liquidity', 'sweep']):
            cascade_events.append({
                'timestamp': timestamp,
                'minutes_from_start': minutes_from_start,
                'context': context,
                'price': movement['price']
            })
        
        # Identify expansion events (directional moves, new highs/lows)
        if any(keyword in context.lower() for keyword in ['expansion', 'formation', 'high', 'delivery']):
            expansion_events.append({
                'timestamp': timestamp,
                'minutes_from_start': minutes_from_start,
                'context': context,
                'price': movement['price']
            })
    
    # Analyze phase transitions for expansion timing
    phase_transitions = nyam_data.get('phase_transitions', [])
    
    print(f"\n📈 Phase Transition Analysis:")
    first_expansion_time = None
    major_cascade_time = None
    
    for phase in phase_transitions:
        phase_type = phase['phase_type']
        start_time = phase['start_time']
        minutes_from_start = _timestamp_to_minutes(start_time, session_start)
        description = phase['description']
        
        print(f"   {start_time} ({minutes_from_start:4.1f} min): {phase_type} - {description}")
        
        # Find first expansion phase
        if phase_type == 'expansion' and first_expansion_time is None:
            first_expansion_time = minutes_from_start
        
        # Find major cascade (significant liquidity event)
        if 'taking' in description.lower() or 'liquidity' in description.lower():
            if major_cascade_time is None or minutes_from_start < major_cascade_time:
                major_cascade_time = minutes_from_start
    
    # Determine dominant session phase from session character
    session_character = nyam_data['price_data'].get('session_character', '')
    
    if 'expansion' in session_character and 'consolidation' in session_character:
        dominant_phase = 'mixed_expansion_consolidation'
    elif 'expansion' in session_character:
        dominant_phase = 'expansion_dominant'
    elif 'consolidation' in session_character:
        dominant_phase = 'consolidation_dominant'
    else:
        dominant_phase = 'neutral'
    
    # Extract the most significant events
    first_major_cascade = cascade_events[0] if cascade_events else None
    first_expansion = expansion_events[0] if expansion_events else None
    
    # Use phase transition timing as primary source
    cascade_timing = first_major_cascade['minutes_from_start'] if first_major_cascade else major_cascade_time
    expansion_timing = first_expansion_time if first_expansion_time else (first_expansion['minutes_from_start'] if first_expansion else None)
    
    print(f"\n🎯 Extracted Event Timing:")
    print(f"   First Major Cascade: {cascade_timing:.1f} minutes")
    print(f"   First Expansion: {expansion_timing:.1f} minutes")
    print(f"   Dominant Phase: {dominant_phase}")
    
    return {
        'cascade_actual_minutes': cascade_timing,
        'expansion_actual_minutes': expansion_timing,
        'dominant_session_phase': dominant_phase,
        'cascade_event_details': first_major_cascade,
        'expansion_event_details': first_expansion,
        'all_cascade_events': cascade_events,
        'all_expansion_events': expansion_events,
        'phase_analysis': phase_transitions
    }

def main():
    """Run corrected validation using actual NYAM Lvl-1 data"""
    
    print("🔍 CORRECTED HMM MONTE CARLO VALIDATION")
    print("=" * 50)
    print("✅ USING ACTUAL NYAM LVL-1 SESSION DATA")
    print("=" * 50)
    
    # Load HMM Monte Carlo prediction
    prediction_data = load_json_data('hmm_monte_carlo_premarket_to_nyam_prediction_20250728_173045.json')
    
    print(f"1️⃣ HMM Monte Carlo Predictions:")
    cascade_predicted = prediction_data['ny_am_predictions']['cascade_timing_minutes']
    expansion_predicted = prediction_data['ny_am_predictions']['expansion_timing_minutes']
    print(f"   Cascade Timing: {cascade_predicted:.1f} minutes")
    print(f"   Expansion Timing: {expansion_predicted:.1f} minutes")
    
    # Load and analyze actual NYAM data
    nyam_data = load_json_data('NYAM_Lvl-1_2025_07_25.json')
    actual_timings = extract_actual_cascade_and_expansion_timing(nyam_data)
    
    # Calculate errors using ACTUAL data
    cascade_actual = actual_timings['cascade_actual_minutes']
    expansion_actual = actual_timings['expansion_actual_minutes']
    
    cascade_error = abs(cascade_predicted - cascade_actual)
    expansion_error = abs(expansion_predicted - expansion_actual)
    
    print(f"\n3️⃣ ACTUAL vs PREDICTED COMPARISON:")
    print(f"   🎯 CASCADE:")
    print(f"      Predicted: {cascade_predicted:.1f} min")
    print(f"      Actual (from NYAM data): {cascade_actual:.1f} min")
    print(f"      Error: {cascade_error:.1f} min")
    
    print(f"   🎯 EXPANSION:")
    print(f"      Predicted: {expansion_predicted:.1f} min") 
    print(f"      Actual (from NYAM data): {expansion_actual:.1f} min")
    print(f"      Error: {expansion_error:.1f} min")
    
    # Compare with previous validation (which used hardcoded values)
    print(f"\n4️⃣ COMPARISON WITH PREVIOUS VALIDATION:")
    print(f"   Previous validation used hardcoded values:")
    print(f"     - Cascade: 120.0 min (hardcoded)")
    print(f"     - Expansion: 26.0 min (hardcoded)")
    print(f"   Actual NYAM data shows:")
    print(f"     - Cascade: {cascade_actual:.1f} min (extracted from session)")
    print(f"     - Expansion: {expansion_actual:.1f} min (extracted from session)")
    
    # Calculate accuracy grades
    def grade_accuracy(error_minutes: float) -> str:
        if error_minutes <= 1.0:
            return "excellent"
        elif error_minutes <= 5.0:
            return "good"
        elif error_minutes <= 10.0:
            return "moderate"
        else:
            return "poor"
    
    cascade_grade = grade_accuracy(cascade_error)
    expansion_grade = grade_accuracy(expansion_error)
    
    print(f"\n5️⃣ ACCURACY ASSESSMENT (Using Actual NYAM Data):")
    print(f"   Cascade Accuracy: {cascade_grade.upper()} ({cascade_error:.1f} min error)")
    print(f"   Expansion Accuracy: {expansion_grade.upper()} ({expansion_error:.1f} min error)")
    
    # Save corrected validation results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"corrected_hmm_validation_actual_nyam_{timestamp}.json"
    
    corrected_results = {
        'validation_metadata': {
            'validation_type': 'corrected_hmm_monte_carlo_with_actual_nyam_data',
            'timestamp': datetime.now().isoformat(),
            'data_source': 'NYAM_Lvl-1_2025_07_25.json',
            'correction_applied': 'extracted_timing_from_actual_session_data'
        },
        'hmm_predictions': {
            'cascade_timing_minutes': cascade_predicted,
            'expansion_timing_minutes': expansion_predicted
        },
        'actual_nyam_events': actual_timings,
        'accuracy_analysis': {
            'cascade_error_minutes': cascade_error,
            'expansion_error_minutes': expansion_error,
            'cascade_grade': cascade_grade,
            'expansion_grade': expansion_grade
        },
        'validation_correction': {
            'previous_hardcoded_values': {
                'cascade': 120.0,
                'expansion': 26.0
            },
            'actual_extracted_values': {
                'cascade': cascade_actual,
                'expansion': expansion_actual
            },
            'difference_from_hardcoded': {
                'cascade_diff': cascade_actual - 120.0,
                'expansion_diff': expansion_actual - 26.0
            }
        }
    }
    
    save_json_data(corrected_results, output_file)
    
    print(f"\n📁 Corrected validation saved: {output_file}")
    
    # Final assessment
    overall_success = cascade_grade in ['excellent', 'good'] and expansion_grade in ['excellent', 'good']
    
    print(f"\n🎯 FINAL ASSESSMENT (Using Actual NYAM Data):")
    if overall_success:
        print(f"   ✅ HMM Monte Carlo method shows good accuracy with actual data")
    else:
        print(f"   ⚠️  Further calibration needed - errors exceed acceptable thresholds")
    
    print(f"\n💡 KEY INSIGHT: Using actual NYAM session data vs hardcoded values")
    print(f"   This validation now reflects true prediction accuracy against real events")
    
    return corrected_results

if __name__ == "__main__":
    main()