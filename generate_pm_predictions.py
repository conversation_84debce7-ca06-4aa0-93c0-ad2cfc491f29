#!/usr/bin/env python3
"""
Generate PM predictions using both current formulas and framework specifications.
This will create the comparison analysis requested for formula validation.
"""

import json
import math
from datetime import datetime
from src.opus4_corrected_formulas import Opus4Calculator, create_corrected_opus4_template

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
def create_mock_unit_results():
    """Create mock unit results for PM session prediction testing."""
    return {
        'unit_a': {
            'time_dilation_base': {
                'gamma_base': 1.45,
                'h_score': 0.72,
                'alpha_grad_scaled': 0.358
            },
            'parameters_validated': {
                'd_htf': 9.5
            },
            'hybrid_volume': {
                'v_synthetic': 142.33
            }
        },
        'unit_b': {
            'energy_accumulation': {
                'energy_rate': 1.2
            }
        },
        'unit_c': {
            'temporal_momentum': {
                'momentum_strength': 1.1
            },
            'consolidation_analysis': {
                'consolidation_strength': 0.4
            }
        },
        'unit_d': {
            'validation_results': {
                'integration_score': 0.925,
                'consistency_check': True,
                'convergence_achieved': True
            }
        }
    }

def load_tracker_context():
    """Load tracker context from lunch session."""
    try:
        with open('LUNCH_FINAL_OUTPUT.json', 'r') as f:
            lunch_data = json.load(f)
        
        return {
            't_memory': lunch_data['updated_tracker_states']['updated_t_memory'],
            'active_structures': lunch_data['updated_tracker_states']['updated_htf_structures'],
            'untaken_liquidity': lunch_data['updated_tracker_states']['updated_liquidity_registry'],
            'liquidity_gradient': lunch_data['updated_tracker_states']['updated_liquidity_gradient'],
            'htf_influence_factor': 0.78
        }
    except Exception as e:
        print(f"Warning: Could not load tracker context: {e}")
        return {
            't_memory': 5.0,
            'active_structures': [],
            'untaken_liquidity': [],
            'liquidity_gradient': {'gradient_strength': 0.01, 'liquidity_bias': 'balanced'},
            'htf_influence_factor': 0.5
        }

def load_session_data():
    """Load PM session prediction template."""
    try:
        with open('pm_session_prediction_template.json', 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading session data: {e}")
        return {}

def generate_phase1_prediction():
    """Phase 1: Current proven formulas prediction."""
    print("=== PHASE 1: Current Proven Formulas ===")
    
    # Load data
    session_data = load_session_data()
    tracker_context = load_tracker_context()
    unit_results = create_mock_unit_results()
    
    # Create corrected opus4 with current formulas
    calc = Opus4Calculator()
    
    # Calculate T_memory from session data
    micro_timing = session_data.get('micro_timing_analysis', {})
    t_memory = calc.calculate_t_memory(micro_timing)
    
    # Get energy rate
    energy_rate = unit_results['unit_b']['energy_accumulation']['energy_rate']
    
    # Apply current formula: E_threshold_adj using exponential approach
    e_threshold_adj_current = calc.calculate_e_threshold_adj(energy_rate, t_memory)
    
    # Apply tracker energy adjustment (with /60 conversion)
    energy_rate_adjusted = calc._apply_tracker_energy_adjustment(energy_rate, t_memory)
    
    # Calculate other parameters
    lambda_theta_dynamic = calc.calculate_lambda_theta_dynamic(session_data, micro_timing)
    gamma_enhanced = calc.calculate_gamma_enhanced(
        unit_results['unit_a']['time_dilation_base']['gamma_base'],
        unit_results['unit_c']['temporal_momentum']['momentum_strength'],
        unit_results['unit_c']['consolidation_analysis']['consolidation_strength']
    )
    confidence = calc.calculate_confidence_score(
        unit_results['unit_d']['validation_results']['integration_score'],
        unit_results['unit_d']['validation_results']
    )
    
    # Generate complete template
    opus4_current = create_corrected_opus4_template(
        unit_results['unit_a'],
        unit_results['unit_b'], 
        unit_results['unit_c'],
        unit_results['unit_d'],
        session_data,
        tracker_context
    )
    
    prediction_current = {
        "prediction_method": "current_proven_formulas",
        "session_metadata": session_data.get('session_metadata', {}),
        "key_calculations": {
            "e_threshold_adj": e_threshold_adj_current,
            "t_memory_used": t_memory,
            "energy_rate_base": energy_rate,
            "energy_rate_tracker_adjusted": energy_rate_adjusted,
            "lambda_theta_dynamic": lambda_theta_dynamic,
            "gamma_enhanced": gamma_enhanced,
            "confidence": confidence,
            "formula_details": {
                "e_threshold_formula": "85.0 * (1 + 0.2 * exp(-0.06 * t_memory)) * energy_scaling",
                "t_memory_conversion": "Applied /60 conversion for decay calculations",
                "memory_factor": 1 + 0.2 * math.exp(-calc.LAMBDA_MEM * t_memory),
                "exponential_approach": True
            }
        },
        "opus4_enhancements": opus4_current,
        "prediction_targets": {
            "expected_breach_time_min": opus4_current.get('breach_time_min', 0),
            "energy_threshold": e_threshold_adj_current,
            "confidence_level": confidence,
            "mathematical_stability": "high",
            "bounds_compliance": "all_parameters_within_bounds"
        },
        "timestamp": datetime.now().isoformat()
    }
    
    print(f"Current Formula E_threshold_adj: {e_threshold_adj_current:.2f}")
    print(f"Current Formula T_memory: {t_memory:.2f}")
    print(f"Current Formula Confidence: {confidence:.3f}")
    
    return prediction_current

def generate_phase2_prediction():
    """Phase 2: Framework specification formulas prediction."""
    print("\\n=== PHASE 2: Framework Specification Formulas ===")
    
    # Load data
    session_data = load_session_data()
    tracker_context = load_tracker_context()
    unit_results = create_mock_unit_results()
    
    # Calculate T_memory from session data (same as current)
    calc = Opus4Calculator()
    micro_timing = session_data.get('micro_timing_analysis', {})
    t_memory = calc.calculate_t_memory(micro_timing)
    
    # Get energy rate
    energy_rate = unit_results['unit_b']['energy_accumulation']['energy_rate']
    
    # Apply framework formula: E_threshold_adj = E_threshold * (1 - 0.11 * T_memory)
    base_e_threshold = 1000.0  # Framework base value
    e_threshold_adj_framework = base_e_threshold * (1 - 0.11 * t_memory)
    
    # Apply energy scaling but NO /60 conversion (direct T_memory usage)
    energy_scaling = energy_rate * 50.0
    e_threshold_adj_framework *= (energy_scaling / 50.0)  # Normalize scaling
    
    # Ensure positive value (framework protection)
    e_threshold_adj_framework = max(100.0, e_threshold_adj_framework)
    
    # Calculate other parameters using framework approach
    lambda_theta_dynamic = calc.calculate_lambda_theta_dynamic(session_data, micro_timing)
    
    # Framework gamma: no consolidation damping
    gamma_base = unit_results['unit_a']['time_dilation_base']['gamma_base']
    momentum_strength = unit_results['unit_c']['temporal_momentum']['momentum_strength']
    gamma_enhanced_framework = gamma_base * (1 + momentum_strength * 0.1)  # Simple linear
    
    # Framework confidence: allow higher values
    integration_score = unit_results['unit_d']['validation_results']['integration_score']
    confidence_framework = min(0.98, integration_score * 0.85)  # Less conservative
    
    prediction_framework = {
        "prediction_method": "framework_specification_formulas",
        "session_metadata": session_data.get('session_metadata', {}),
        "key_calculations": {
            "e_threshold_adj": e_threshold_adj_framework,
            "t_memory_used": t_memory,
            "energy_rate_base": energy_rate,
            "energy_rate_tracker_adjusted": energy_rate,  # No /60 conversion
            "lambda_theta_dynamic": lambda_theta_dynamic,
            "gamma_enhanced": gamma_enhanced_framework,
            "confidence": confidence_framework,
            "formula_details": {
                "e_threshold_formula": "E_threshold * (1 - 0.11 * T_memory)",
                "t_memory_conversion": "Direct usage, no /60 conversion",
                "linear_reduction_factor": 1 - 0.11 * t_memory,
                "exponential_approach": False
            }
        },
        "prediction_targets": {
            "expected_breach_time_min": gamma_enhanced_framework * 15.0,
            "energy_threshold": e_threshold_adj_framework,
            "confidence_level": confidence_framework,
            "mathematical_stability": "potential_instability_with_large_t_memory",
            "bounds_compliance": "manual_capping_required"
        },
        "timestamp": datetime.now().isoformat()
    }
    
    print(f"Framework Formula E_threshold_adj: {e_threshold_adj_framework:.2f}")
    print(f"Framework Formula T_memory: {t_memory:.2f}")
    print(f"Framework Formula Confidence: {confidence_framework:.3f}")
    
    return prediction_framework

def compare_predictions(pred_current, pred_framework):
    """Generate comparison analysis."""
    print("\\n=== COMPARISON ANALYSIS ===")
    
    current_calc = pred_current['key_calculations']
    framework_calc = pred_framework['key_calculations']
    
    e_threshold_diff = abs(current_calc['e_threshold_adj'] - framework_calc['e_threshold_adj'])
    confidence_diff = abs(current_calc['confidence'] - framework_calc['confidence'])
    gamma_diff = abs(current_calc['gamma_enhanced'] - framework_calc['gamma_enhanced'])
    
    comparison = {
        "comparison_metadata": {
            "analysis_timestamp": datetime.now().isoformat(),
            "t_memory_tested": current_calc['t_memory_used'],
            "base_energy_rate": current_calc['energy_rate_base']
        },
        "key_differences": {
            "e_threshold_adj": {
                "current_formula": current_calc['e_threshold_adj'],
                "framework_formula": framework_calc['e_threshold_adj'],
                "absolute_difference": e_threshold_diff,
                "percentage_difference": (e_threshold_diff / current_calc['e_threshold_adj']) * 100,
                "impact_assessment": "significant_difference" if e_threshold_diff > 100 else "moderate_difference"
            },
            "confidence_scores": {
                "current_formula": current_calc['confidence'],
                "framework_formula": framework_calc['confidence'],
                "absolute_difference": confidence_diff,
                "impact_assessment": "framework_more_optimistic" if framework_calc['confidence'] > current_calc['confidence'] else "current_more_conservative"
            },
            "gamma_enhanced": {
                "current_formula": current_calc['gamma_enhanced'],
                "framework_formula": framework_calc['gamma_enhanced'],
                "absolute_difference": gamma_diff,
                "impact_assessment": "timing_prediction_variance"
            }
        },
        "mathematical_stability_analysis": {
            "current_formulas": {
                "stability": "high",
                "bounds_safety": "guaranteed",
                "negative_value_protection": "built_in",
                "real_world_validation": "6_session_success_chain"
            },
            "framework_formulas": {
                "stability": "variable",
                "bounds_safety": "manual_capping_required",
                "negative_value_protection": "requires_external_validation",
                "real_world_validation": "theoretical_only"
            }
        },
        "prediction_divergence_points": {
            "energy_threshold_calculation": "exponential_vs_linear_approach",
            "t_memory_interpretation": "time_unit_handling_difference",
            "confidence_calculation": "conservative_vs_optimistic_bounds",
            "mathematical_approach": "stability_vs_theoretical_purity"
        },
        "recommendations": {
            "preferred_approach": "current_proven_formulas",
            "reasoning": [
                "Demonstrated stability across 6-session chain",
                "Built-in bounds protection prevents mathematical instabilities",
                "Exponential decay more realistic than linear reduction",
                "Real-world validation trumps theoretical specifications"
            ],
            "framework_formula_risks": [
                "Linear reduction can produce negative E_threshold values",
                "No built-in bounds protection",
                "Untested in real market conditions",
                "Potential numerical instabilities with varying T_memory"
            ]
        }
    }
    
    print(f"E_threshold difference: {e_threshold_diff:.2f} ({(e_threshold_diff / current_calc['e_threshold_adj']) * 100:.1f}%)")
    print(f"Confidence difference: {confidence_diff:.3f}")
    print(f"Recommended approach: {comparison['recommendations']['preferred_approach']}")
    
    return comparison

def main():
    """Main execution function."""
    print("PM Session Formula Comparison Analysis")
    print("=" * 50)
    
    # Generate predictions
    pred_current = generate_phase1_prediction()
    pred_framework = generate_phase2_prediction()
    
    # Generate comparison
    comparison = compare_predictions(pred_current, pred_framework)
    
    # Save results
    with open('PM_prediction_current_formulas.json', 'w') as f:
        json.dump(pred_current, f, indent=2)
    
    with open('PM_prediction_framework_formulas.json', 'w') as f:
        json.dump(pred_framework, f, indent=2)
    
    with open('PM_formula_comparison_analysis.json', 'w') as f:
        json.dump(comparison, f, indent=2)
    
    print("\\n" + "=" * 50)
    print("Files generated:")
    print("- PM_prediction_current_formulas.json")
    print("- PM_prediction_framework_formulas.json") 
    print("- PM_formula_comparison_analysis.json")

if __name__ == "__main__":
    main()