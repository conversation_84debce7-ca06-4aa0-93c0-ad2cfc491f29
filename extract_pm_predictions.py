#!/usr/bin/env python3
"""
Extract and compare the specific PM session predictions from both formula approaches.
What does each approach actually predict will happen?
"""

import json
from datetime import datetime, timedelta

import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
def load_comparison_data():
    """Load the comparison results."""
    with open('PM_both_approaches_comparison.json', 'r') as f:
        return json.load(f)

def calculate_specific_predictions(approach_data, approach_name):
    """Calculate specific predictions for PM session from formula results."""
    calcs = approach_data['key_calculations']
    
    # Current price from lunch close
    current_price = 23198.75
    
    # Extract key parameters
    e_threshold = calcs['e_threshold_adj']
    gamma_enhanced = calcs['gamma_enhanced']
    confidence = calcs['confidence']
    
    # Calculate breach timing
    breach_time_min = gamma_enhanced * 15.0  # Standard calculation
    
    # Calculate energy-based move expectations
    # Higher E_threshold = more energy needed = bigger moves expected
    energy_move_factor = e_threshold / 1000.0  # Normalize to 1000 baseline
    expected_range = 60 * energy_move_factor  # Base 60-point range
    
    # Calculate directional bias (using tracker context)
    liquidity_bias = "upward_pull"  # From tracker data
    
    # Time calculations for PM session (13:00-16:00 ET = 180 minutes)
    pm_start = "13:00 ET"
    breach_time_actual = f"{13 + int(breach_time_min//60)}:{int(breach_time_min%60):02d} ET"
    
    # Target levels based on energy threshold
    if approach_name == "current_proven":
        # Higher E_threshold = expect more substantial moves
        upward_target_1 = current_price + (expected_range * 0.6)  # Conservative
        upward_target_2 = current_price + expected_range  # Full range
        downward_target = current_price - (expected_range * 0.4)  # Less likely due to upward bias
    else:
        # Lower E_threshold = expect smaller, quicker moves
        upward_target_1 = current_price + (expected_range * 0.8)  # Quicker to targets
        upward_target_2 = current_price + (expected_range * 1.2)  # Overshoot potential
        downward_target = current_price - (expected_range * 0.6)  # More downside risk
    
    return {
        "approach": approach_name,
        "timing_predictions": {
            "expected_first_move": f"Within {breach_time_min:.1f} minutes of PM open",
            "breach_time": breach_time_actual,
            "session_character": "consolidation_then_expansion" if e_threshold > 800 else "quick_expansion_moves"
        },
        "price_targets": {
            "current_price": current_price,
            "expected_range": expected_range,
            "upward_target_conservative": upward_target_1,
            "upward_target_full": upward_target_2,
            "downward_target": downward_target,
            "primary_direction": liquidity_bias
        },
        "energy_expectations": {
            "e_threshold": e_threshold,
            "energy_interpretation": "high_energy_needed" if e_threshold > 800 else "moderate_energy_sufficient",
            "move_magnitude": "substantial_moves" if e_threshold > 800 else "moderate_moves",
            "consolidation_time": f"{breach_time_min:.1f} minutes before major moves"
        },
        "confidence_assessment": {
            "confidence_level": confidence,
            "prediction_reliability": "high" if confidence > 0.8 else "moderate" if confidence > 0.7 else "conservative",
            "risk_level": "low" if confidence > 0.8 else "moderate"
        },
        "session_scenario": {
            "most_likely": f"PM opens, consolidates ~{breach_time_min:.0f}min, then moves toward {upward_target_1:.2f}",
            "upside_scenario": f"Strong move to {upward_target_2:.2f} if energy builds",
            "downside_scenario": f"Rejection back to {downward_target:.2f} if energy fails",
            "key_inflection": f"Watch for energy buildup around {breach_time_actual}"
        }
    }

def compare_predictions(current_pred, framework_pred):
    """Compare what each approach predicts will happen."""
    print("=== PM SESSION PREDICTION COMPARISON ===")
    print("Current Price: 23,198.75 (Lunch Close)")
    print("PM Session: 13:00-16:00 ET (180 minutes)")
    print()
    
    print("CURRENT PROVEN FORMULAS PREDICT:")
    print(f"• First Move: {current_pred['timing_predictions']['expected_first_move']}")
    print(f"• Key Time: {current_pred['timing_predictions']['breach_time']}")
    print(f"• Expected Range: {current_pred['price_targets']['expected_range']:.1f} points")
    print(f"• Conservative Target: {current_pred['price_targets']['upward_target_conservative']:.2f}")
    print(f"• Full Target: {current_pred['price_targets']['upward_target_full']:.2f}")
    print(f"• Energy Needed: {current_pred['energy_expectations']['energy_interpretation']}")
    print(f"• Confidence: {current_pred['confidence_assessment']['prediction_reliability']}")
    print(f"• Scenario: {current_pred['session_scenario']['most_likely']}")
    print()
    
    print("FRAMEWORK SPECIFICATION FORMULAS PREDICT:")
    print(f"• First Move: {framework_pred['timing_predictions']['expected_first_move']}")
    print(f"• Key Time: {framework_pred['timing_predictions']['breach_time']}")
    print(f"• Expected Range: {framework_pred['price_targets']['expected_range']:.1f} points")
    print(f"• Conservative Target: {framework_pred['price_targets']['upward_target_conservative']:.2f}")
    print(f"• Full Target: {framework_pred['price_targets']['upward_target_full']:.2f}")
    print(f"• Energy Needed: {framework_pred['energy_expectations']['energy_interpretation']}")
    print(f"• Confidence: {framework_pred['confidence_assessment']['prediction_reliability']}")
    print(f"• Scenario: {framework_pred['session_scenario']['most_likely']}")
    print()
    
    # Key differences
    range_diff = abs(current_pred['price_targets']['expected_range'] - framework_pred['price_targets']['expected_range'])
    timing_diff = abs(
        float(current_pred['timing_predictions']['expected_first_move'].split()[1]) - 
        float(framework_pred['timing_predictions']['expected_first_move'].split()[1])
    )
    target_diff = abs(
        current_pred['price_targets']['upward_target_conservative'] - 
        framework_pred['price_targets']['upward_target_conservative']
    )
    
    print("KEY PREDICTION DIFFERENCES:")
    print(f"• Range Expectation: {range_diff:.1f} point difference")
    print(f"• Timing: {timing_diff:.1f} minute difference")
    print(f"• Target Levels: {target_diff:.1f} point difference")
    print(f"• Energy Interpretation: Different thresholds change expected magnitude")
    print(f"• Risk Assessment: Framework more optimistic, Current more conservative")
    
    return {
        "current_formulas_prediction": current_pred,
        "framework_formulas_prediction": framework_pred,
        "key_differences": {
            "range_difference_points": range_diff,
            "timing_difference_minutes": timing_diff,
            "target_difference_points": target_diff,
            "energy_interpretation_difference": "current_expects_bigger_moves",
            "confidence_difference": "framework_more_optimistic"
        }
    }

def main():
    """Extract and compare specific PM predictions."""
    print("PM SESSION: WHAT EACH FORMULA APPROACH PREDICTS WILL HAPPEN")
    print("=" * 65)
    
    # Load comparison data
    comparison_data = load_comparison_data()
    
    # Extract predictions
    current_pred = calculate_specific_predictions(
        comparison_data['phase1_current_formulas'], 
        "current_proven"
    )
    
    framework_pred = calculate_specific_predictions(
        comparison_data['phase2_framework_formulas'], 
        "framework_specification"
    )
    
    # Compare and display
    comparison = compare_predictions(current_pred, framework_pred)
    
    # Save detailed predictions
    with open('PM_specific_predictions_comparison.json', 'w') as f:
        json.dump({
            "prediction_comparison": comparison,
            "analysis_timestamp": datetime.now().isoformat(),
            "session_context": {
                "current_price": 23198.75,
                "session_time": "13:00-16:00 ET",
                "tracker_state": "6_session_chain_active",
                "liquidity_bias": "upward_pull"
            }
        }, f, indent=2)
    
    print()
    print("=" * 65)
    print("File generated: PM_specific_predictions_comparison.json")
    print()
    print("SUMMARY:")
    print("Current Formulas: Expect substantial consolidation, then strong directional moves")
    print("Framework Formulas: Expect quicker, smaller moves with higher confidence")
    print("Key Risk: 38% difference in energy thresholds = different move expectations")

if __name__ == "__main__":
    main()