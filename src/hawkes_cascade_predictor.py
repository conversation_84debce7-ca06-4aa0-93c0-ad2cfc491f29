#!/usr/bin/env python3
"""
Hawkes Process Cascade Predictor
Implements Grok 4's self-exciting point process for cascade timing prediction
using ICT event sequences and dynamic synthetic volume.
"""

import numpy as np
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
import sys
sys.path.append('.')
from src.utils import load_json_data, save_json_data
from src.dynamic_synthetic_volume import DynamicSyntheticVolumeCalculator, MarketEvent
from src.cache_manager import get_unified_cache

@dataclass
class HawkesParameters:
    """Hawkes process parameters for cascade prediction"""
    mu: float          # Baseline intensity
    alpha: float       # Excitation coefficient
    beta: float        # Decay rate
    threshold: float   # Cascade initiation threshold

@dataclass
class IntensityPoint:
    """Point in time with calculated Hawkes intensity"""
    time_minutes: float
    intensity: float
    contributing_events: List[str]
    excitation_sources: List[Tuple[float, float]]  # (time, contribution)

@dataclass
class CascadePrediction:
    """Hawkes process cascade prediction result"""
    predicted_cascade_time: float
    prediction_confidence: float
    intensity_buildup: List[IntensityPoint]
    triggering_events: List[MarketEvent]
    threshold_crossed_at: float
    methodology: str
    parameters_used: HawkesParameters

class HawkesCascadePredictor:
    """
    Predict cascade timing using Hawkes self-exciting point process
    with ICT event sequences and dynamic synthetic volume.
    """
    
    def __init__(self):
        self.volume_calculator = DynamicSyntheticVolumeCalculator()
        
        # 🚀 UNIFIED CACHE: Replace local caching with unified system
        self.unified_cache = get_unified_cache()
        
        # Default Hawkes parameters (Grok 4 recommended)
        self.default_params = HawkesParameters(
            mu=0.5,        # Baseline intensity from volatility
            alpha=0.6,     # ICT-tuned excitation for liquidity chains
            beta=0.02,     # 60-minute decay for consolidations
            threshold=0.75 # 1.5 × μ for cascade initiation
        )
    
    def calculate_session_parameters(self, session_data: dict) -> HawkesParameters:
        """Calculate session-specific Hawkes parameters"""
        
        # Extract session characteristics
        session_range = session_data.get('price_data', {}).get('range', 100.0)
        session_character = session_data.get('price_data', {}).get('session_character', '')
        
        # Calculate μ (baseline) from volatility factor
        volatility_factor = min(1.0, session_range / 200.0)  # Normalize to 0-1
        mu = 0.5 * volatility_factor
        
        # Adjust α (excitation) based on session character
        alpha = 0.6  # Base value
        if 'expansion' in session_character.lower():
            alpha *= 1.2  # Higher excitation for expansion sessions
        elif 'consolidation' in session_character.lower():
            alpha *= 0.8  # Lower excitation for consolidation
        
        # Adjust β (decay) based on session duration and character
        beta = 0.02  # Base 60-minute decay
        if 'consolidation' in session_character.lower():
            beta *= 0.5  # Slower decay for consolidation buildup
        
        # Set threshold relative to baseline
        threshold = 1.5 * mu
        
        return HawkesParameters(
            mu=mu,
            alpha=alpha,
            beta=beta,
            threshold=threshold
        )
    
    def calculate_hawkes_intensity(self, time_minutes: float, events: List[MarketEvent], 
                                 session_data: dict, params: HawkesParameters) -> IntensityPoint:
        """
        Calculate Hawkes intensity at specific time:
        λ(t) = μ + Σ α × v_s(t_i) × e^(-β(t - t_i))
        """
        
        # Start with baseline intensity
        intensity = params.mu
        contributing_events = []
        excitation_sources = []
        
        # Add excitation from past events
        for event in events:
            if event.minutes_from_start < time_minutes:
                # Calculate time since event
                time_since = time_minutes - event.minutes_from_start
                
                # Get dynamic synthetic volume for this event
                v_s = self.volume_calculator.get_volume_for_timestamp(
                    session_data, event.timestamp
                )
                
                # Calculate excitation contribution
                excitation = params.alpha * (v_s / 100.0) * np.exp(-params.beta * time_since)
                
                intensity += excitation
                contributing_events.append(f"{event.timestamp}_{event.event_type}")
                excitation_sources.append((event.minutes_from_start, excitation))
        
        return IntensityPoint(
            time_minutes=time_minutes,
            intensity=intensity,
            contributing_events=contributing_events,
            excitation_sources=excitation_sources
        )
    
    def predict_cascade_timing(self, session_data: dict, 
                              premarket_context: Optional[dict] = None) -> CascadePrediction:
        """
        Predict cascade timing using Hawkes process with ICT event buildup
        """
        
        print("🎯 HAWKES CASCADE PREDICTION")
        print("=" * 40)
        
        # Extract market events
        events = self.volume_calculator.extract_market_events(session_data)
        print(f"1️⃣ Analyzing {len(events)} market events")
        
        # Calculate session-specific parameters
        params = self.calculate_session_parameters(session_data)
        
        print(f"2️⃣ Hawkes Parameters:")
        print(f"   μ (baseline): {params.mu:.3f}")
        print(f"   α (excitation): {params.alpha:.3f}")
        print(f"   β (decay): {params.beta:.3f}")
        print(f"   Threshold: {params.threshold:.3f}")
        
        # Calculate intensity over time (minute by minute)
        session_duration = session_data.get('session_metadata', {}).get('duration_minutes', 150)
        intensity_timeline = []
        cascade_time = None
        threshold_crossed = None
        
        print(f"\n3️⃣ Intensity Buildup Analysis:")
        
        # 🚨 CRITICAL OPTIMIZATION: Early termination to prevent 90,000 operation catastrophe
        cascade_detected = False
        max_iterations = min(session_duration, 180)
        
        for t in range(0, max_iterations, 1):
            intensity_point = self.calculate_hawkes_intensity(t, events, session_data, params)
            intensity_timeline.append(intensity_point)
            
            # 🎯 EARLY TERMINATION #1: Cascade threshold crossed with high confidence
            if cascade_time is None and intensity_point.intensity >= params.threshold:
                cascade_time = t
                threshold_crossed = intensity_point.intensity
                cascade_detected = True
                
                print(f"   🎯 Cascade threshold crossed at {t} minutes")
                print(f"   🔥 Intensity: {intensity_point.intensity:.3f} (threshold: {params.threshold:.3f})")
                print(f"   📈 Contributing events: {len(intensity_point.contributing_events)}")
                
                # Exit early if cascade is clearly established
                if intensity_point.intensity > params.threshold * 2.0:
                    print(f"   ⚡ EARLY EXIT: Strong cascade signal (intensity > 2x threshold)")
                    break
            
            # 🎯 EARLY TERMINATION #2: Convergence detection - intensity has plateaued
            if len(intensity_timeline) >= 10:
                recent_intensities = [p.intensity for p in intensity_timeline[-10:]]
                intensity_range = max(recent_intensities) - min(recent_intensities)
                if intensity_range < 0.01:
                    print(f"   ⚡ EARLY EXIT: Intensity converged at {t} minutes (range: {intensity_range:.4f})")
                    break
            
            # 🎯 EARLY TERMINATION #3: Low intensity - cascade unlikely
            if t > 30:
                max_intensity_so_far = max(p.intensity for p in intensity_timeline)
                if max_intensity_so_far < params.threshold * 0.5:
                    print(f"   ⚡ EARLY EXIT: Low cascade probability at {t} minutes (max: {max_intensity_so_far:.3f})")
                    break
                    
            # 🎯 EARLY TERMINATION #4: Diminishing returns check
            if t > 60 and len(intensity_timeline) >= 20:
                recent_max = max(p.intensity for p in intensity_timeline[-20:])
                if recent_max < params.threshold * 0.7:
                    print(f"   ⚡ EARLY EXIT: Diminishing intensity trend at {t} minutes")
                    break
        
        print(f"   📊 Completed {len(intensity_timeline)} iterations (max: {max_iterations})")
        
        # If no threshold crossing found, predict based on maximum intensity
        if cascade_time is None and intensity_timeline:
            max_intensity_point = max(intensity_timeline, key=lambda x: x.intensity)
            cascade_time = max_intensity_point.time_minutes
            threshold_crossed = max_intensity_point.intensity
            
            print(f"   ⚠️ No threshold crossing - using max intensity at {cascade_time} minutes")
        
        # Calculate prediction confidence
        if threshold_crossed >= params.threshold:
            confidence = min(0.95, threshold_crossed / params.threshold - 0.1)
        else:
            confidence = max(0.1, threshold_crossed / params.threshold * 0.5)
        
        # Identify key triggering events (events within 5 minutes of prediction)
        triggering_events = [
            event for event in events 
            if abs(event.minutes_from_start - cascade_time) <= 5.0
        ]
        
        print(f"\n4️⃣ Prediction Results:")
        print(f"   🎯 Predicted Cascade Time: {cascade_time:.1f} minutes")
        print(f"   📊 Prediction Confidence: {confidence:.2f}")
        print(f"   🔗 Triggering Events: {len(triggering_events)}")
        
        return CascadePrediction(
            predicted_cascade_time=cascade_time,
            prediction_confidence=confidence,
            intensity_buildup=intensity_timeline,
            triggering_events=triggering_events,
            threshold_crossed_at=threshold_crossed,
            methodology="hawkes_process_with_dynamic_synthetic_volume",
            parameters_used=params
        )
    
    def validate_against_actual(self, prediction: CascadePrediction, 
                               actual_cascade_time: float) -> Dict[str, Any]:
        """Validate Hawkes prediction against actual cascade timing"""
        
        prediction_error = abs(prediction.predicted_cascade_time - actual_cascade_time)
        
        # Calculate accuracy grade
        if prediction_error <= 1.0:
            grade = "excellent"
        elif prediction_error <= 5.0:
            grade = "good"
        elif prediction_error <= 10.0:
            grade = "moderate"
        else:
            grade = "poor"
        
        # Compare with static methods
        static_monte_carlo_error = abs(8.1 - actual_cascade_time)  # Previous Monte Carlo
        hmm_prediction_error = abs(202.5 - actual_cascade_time)    # Previous HMM
        
        hawkes_improvement_vs_monte_carlo = ((static_monte_carlo_error - prediction_error) / static_monte_carlo_error) * 100
        hawkes_improvement_vs_hmm = ((hmm_prediction_error - prediction_error) / hmm_prediction_error) * 100
        
        return {
            'hawkes_prediction_minutes': prediction.predicted_cascade_time,
            'actual_cascade_minutes': actual_cascade_time,
            'prediction_error_minutes': prediction_error,
            'accuracy_grade': grade,
            'prediction_confidence': prediction.prediction_confidence,
            'comparison_with_other_methods': {
                'static_monte_carlo_error': static_monte_carlo_error,
                'hmm_prediction_error': hmm_prediction_error,
                'hawkes_improvement_vs_monte_carlo': hawkes_improvement_vs_monte_carlo,
                'hawkes_improvement_vs_hmm': hawkes_improvement_vs_hmm
            },
            'hawkes_effectiveness': {
                'threshold_crossing_achieved': prediction.threshold_crossed_at >= prediction.parameters_used.threshold,
                'intensity_buildup_detected': len(prediction.intensity_buildup) > 0,
                'triggering_events_identified': len(prediction.triggering_events) > 0
            }
        }

def test_hawkes_cascade_predictor():
    """Test Hawkes cascade predictor on NYAM session with known cascade at 8 minutes"""
    
    print("🚀 TESTING HAWKES CASCADE PREDICTOR")
    print("=" * 50)
    
    # Load NYAM session data
    nyam_data = load_json_data('NYAM_Lvl-1_2025_07_25.json')
    
    # Initialize predictor
    predictor = HawkesCascadePredictor()
    
    # Generate prediction
    prediction = predictor.predict_cascade_timing(nyam_data)
    
    # Validate against known cascade at 8 minutes (09:38:00)
    actual_cascade_time = 8.0  # From ground truth analysis
    validation_results = predictor.validate_against_actual(prediction, actual_cascade_time)
    
    print(f"\n📊 VALIDATION RESULTS:")
    print(f"   🎯 Hawkes Prediction: {validation_results['hawkes_prediction_minutes']:.1f} minutes")
    print(f"   ✅ Actual Cascade: {validation_results['actual_cascade_minutes']:.1f} minutes")
    print(f"   📏 Error: {validation_results['prediction_error_minutes']:.1f} minutes ({validation_results['accuracy_grade']})")
    print(f"   📈 Confidence: {validation_results['prediction_confidence']:.2f}")
    
    print(f"\n🔄 METHOD COMPARISON:")
    comparisons = validation_results['comparison_with_other_methods']
    print(f"   Monte Carlo Error: {comparisons['static_monte_carlo_error']:.1f} min")
    print(f"   HMM Error: {comparisons['hmm_prediction_error']:.1f} min")
    print(f"   Hawkes vs Monte Carlo: {comparisons['hawkes_improvement_vs_monte_carlo']:.1f}% improvement")
    print(f"   Hawkes vs HMM: {comparisons['hawkes_improvement_vs_hmm']:.1f}% improvement")
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"hawkes_cascade_prediction_validation_{timestamp}.json"
    
    results_data = {
        'prediction_metadata': {
            'methodology': 'hawkes_process_cascade_prediction',
            'timestamp': datetime.now().isoformat(),
            'session_analyzed': 'NYAM_Lvl-1_2025_07_25.json',
            'challenge_addressed': 'cascade_vs_expansion_timing_calibration'
        },
        'hawkes_prediction': {
            'predicted_cascade_time': prediction.predicted_cascade_time,
            'prediction_confidence': prediction.prediction_confidence,
            'parameters_used': {
                'mu': prediction.parameters_used.mu,
                'alpha': prediction.parameters_used.alpha,
                'beta': prediction.parameters_used.beta,
                'threshold': prediction.parameters_used.threshold
            },
            'triggering_events_count': len(prediction.triggering_events),
            'intensity_timeline_points': len(prediction.intensity_buildup)
        },
        'validation_results': validation_results,
        'hawkes_effectiveness_summary': {
            'method_success': validation_results['accuracy_grade'] in ['excellent', 'good'],
            'improvement_over_hmm': comparisons['hawkes_improvement_vs_hmm'] > 50,
            'improvement_over_monte_carlo': comparisons['hawkes_improvement_vs_monte_carlo'] > 0,
            'ready_for_deployment': validation_results['prediction_error_minutes'] < 15.0
        }
    }
    
    save_json_data(results_data, output_file)
    
    print(f"\n📁 Results saved: {output_file}")
    
    # Final assessment
    if validation_results['accuracy_grade'] in ['excellent', 'good']:
        print(f"\n🎉 SUCCESS: Hawkes process shows {validation_results['accuracy_grade']} cascade prediction!")
        print(f"   Ready for integration into production pipeline.")
    else:
        print(f"\n⚠️ NEEDS CALIBRATION: Further parameter tuning required.")
        print(f"   Consider adjusting α, β, or threshold values.")
    
    return prediction, validation_results

if __name__ == "__main__":
    test_hawkes_cascade_predictor()