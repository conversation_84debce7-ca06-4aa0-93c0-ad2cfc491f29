#!/usr/bin/env python3

# POST-PREDICTION HOOK IMPLEMENTATION
# Add this to your validation pipeline:

def post_prediction_hook(predicted_close, actual_close, session_range, session_data=None):
    """Automatic hook that triggers on significant prediction misses"""
    
    prediction_error = abs(predicted_close - actual_close)
    error_threshold = 0.2 * session_range  # 20% of session range
    
    if prediction_error > error_threshold:
        print(f"🚨 Significant prediction miss detected: {prediction_error:.1f} points")
        
        # Trigger automatic Grok 4 analysis
        from automatic_pattern_analysis import AutomaticPatternAnalyzer
        analyzer = AutomaticPatternAnalyzer()
        
        analyzer.analyze_pattern_miss({
            "predicted": predicted_close,
            "actual": actual_close,
            "error": prediction_error,
            "range": session_range,
            "session_data": session_data,
            "focus": "fpfvg_cascade_patterns",
            "question": "What mathematical relationship predicts this cascade?"
        })
        
        return True  # Analysis triggered
    
    return False  # No analysis needed

# USAGE EXAMPLE:
# After any prediction validation:
triggered = post_prediction_hook(23285.73, 23350.50, 162.5, session_data)
