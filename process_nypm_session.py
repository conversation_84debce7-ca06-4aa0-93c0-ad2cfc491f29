#!/usr/bin/env python3
"""
Process NYPM Session July 25th 2025 - Fresh grokEnhanced Generation
Uses optimized A→B→C→D pipeline with architectural improvements from refactor-architect agent
"""

import os
import sys
from src.pipeline import GrokPipeline
from src.utils import load_json_data, save_json_data

def main():
    print('🚀 PROCESSING NYPM SESSION - July 25th 2025')
    print('=' * 55)

    try:
        # Ensure API key is set
        api_key = os.getenv('GROK_API_KEY')
        if not api_key:
            print('❌ GROK_API_KEY environment variable not set')
            return False
        
        # Load NYPM session data
        session_data = load_json_data('NYPM_Lvl-1_2025_07_25.json')
        print('✅ Loaded NYPM_Lvl-1_2025_07_25.json')
        print(f'   Session: {session_data["session_metadata"]["session_type"]} ({session_data["session_metadata"]["start_time"]} - {session_data["session_metadata"]["end_time"]})')
        print(f'   Price movements: {len(session_data.get("price_movements", []))} events')
        print(f'   Session character: {session_data["session_metadata"]["session_character"]}')
        print(f'   Price range: {session_data["price_data"]["range"]} points ({session_data["price_data"]["low"]} - {session_data["price_data"]["high"]})')
        
        # Create pipeline with architectural improvements
        pipeline = GrokPipeline(api_key)
        print('✅ Created optimized pipeline with refactor-architect improvements')
        
        # Process through A→B→C→D pipeline
        print('📊 Processing through Units A→B→C→D...')
        results = pipeline.process_session(session_data, {})
        
        # Save results
        output_file = 'NYPM_grokEnhanced_2025_07_25.json'
        save_json_data(results, output_file)
        
        print('')
        print('🎯 NYPM PROCESSING COMPLETED SUCCESSFULLY!')
        print('=' * 50)
        
        # Show processing metadata
        if 'pipeline_metadata' in results:
            metadata = results['pipeline_metadata']
            print(f'✅ Total processing time: {metadata.get("total_processing_time_ms", 0)}ms')
            print(f'✅ Equations processed: {metadata.get("equations_processed", 0)}')
            
            # Show unit times
            unit_times = metadata.get('individual_unit_times', {})
            for unit, time_ms in unit_times.items():
                print(f'   Unit {unit}: {time_ms}ms')
        
        # Check enhanced calculations quality
        if 'grok_enhanced_calculations' in results:
            enhanced = results['grok_enhanced_calculations']
            
            # Unit A foundation data
            unit_a = enhanced.get('unit_a_foundation', {})
            hybrid_volume = unit_a.get('hybrid_volume', {})
            v_synthetic = hybrid_volume.get('v_synthetic', 'N/A')
            
            # Unit D validation data  
            unit_d = enhanced.get('unit_d_integration_validation', {})
            extracted = unit_d.get('extracted_values', {})
            
            print('')
            print('🔬 Enhanced Data Quality Check:')
            print(f'   V_synthetic: {v_synthetic} (dynamic Hawkes)')
            print(f'   Energy rate: {extracted.get("energy_rate", "N/A")}')
            print(f'   Momentum strength: {extracted.get("momentum_strength", "N/A")}')
            print(f'   Alpha_t: {extracted.get("alpha_t", "N/A")}')
            print(f'   Structural integrity: {extracted.get("structural_integrity", "N/A")}')
            
            # Validate real calculations (not fallbacks)
            energy_rate = extracted.get('energy_rate', 1.0)
            energy_ok = False
            if energy_rate != 'N/A':
                try:
                    energy_ok = abs(float(energy_rate) - 1.0) > 0.001
                except (ValueError, TypeError):
                    energy_ok = False
            
            volume_ok = False
            if isinstance(v_synthetic, (int, float)):
                volume_ok = abs(float(v_synthetic) - 128.66) > 1.0
                
            print('')
            print('✅ Validation Results:')
            print(f'   Real energy calculation: {"✅ PASS" if energy_ok else "❌ FALLBACK"}')
            print(f'   Dynamic volume calculation: {"✅ PASS" if volume_ok else "❌ STATIC"}')
        
        # Check for pipeline errors
        if 'pipeline_error' in results:
            error_info = results['pipeline_error']
            print('')
            print('⚠️  Pipeline Issues Detected:')
            print(f'   Error: {error_info.get("error_message", "Unknown")}')
            print(f'   Status: {error_info.get("pipeline_status", "Unknown")}')
            print(f'   Severity: {error_info.get("error_severity", "Unknown")}')
        
        print('')
        print(f'📄 Output file: {output_file}')
        print('✅ FRESH NYPM grokEnhanced FILE GENERATED!')
        
        return True
        
    except Exception as e:
        print(f'❌ NYPM processing failed: {str(e)}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)