# Asia Session Lvl-1 Processing Prompt for Claude

## TASK INSTRUCTION

You are tasked with processing the Asia trading session from Sunday 19:00 ET (Monday Asia morning) into a standardized Lvl-1 JSON format. This session follows a major weekend gap scenario where Friday close was 23447.75 and Sunday NWOG was 23520.00 (+72.25 points bullish gap).

**CRITICAL**: Output ONLY valid JSON. Do not include explanations, commentary, or markdown formatting.

## INPUT DATA TO ANALYZE

**Market Context:**
- Friday Electronic Close (16:59 ET): 23447.75
- Sunday Electronic Open (18:00 ET): 23520.00  
- Weekend Gap: +72.25 points (major bullish gap)
- Asia Session Start: Sunday 19:00 ET
- Expected Cascade: ~4.5 seconds from session start (based on NWOG prediction)
- Gap Fill Zone: 23447.75 - 23520.00 (72.2% fill probability)

**Session Data to Process:**
[INSERT ACTUAL ASIA SESSION PRICE ACTION, TIMESTAMPS, AND MARKET EVENTS HERE]

## REQUIRED JSON OUTPUT FORMAT

```json
{
  "session_metadata": {
    "session_id": "asia_session_2025_07_28",
    "session_type": "Asia",
    "date": "2025-07-28",
    "start_time": "19:00:00 ET",
    "end_time": "[SESSION_END_TIME] ET",
    "duration_minutes": [ACTUAL_DURATION],
    "sequence_position": 1,
    "timezone": "ET",
    "session_character": "[DESCRIBE_SESSION_CHARACTER]",
    "trading_hours": {
      "start_time": "19:00",
      "end_time": "[SESSION_END]",
      "timezone": "ET"
    },
    "weekend_context": {
      "friday_close": 23447.75,
      "sunday_nwog": 23520.00,
      "weekend_gap_points": 72.25,
      "gap_classification": "major_bullish_gap",
      "gap_fill_expectations": "high_probability_downward"
    }
  },
  "price_data": {
    "open": [ASIA_OPEN_PRICE],
    "high": [SESSION_HIGH],
    "low": [SESSION_LOW], 
    "close": [SESSION_CLOSE],
    "range": [HIGH_MINUS_LOW],
    "session_character": "[DETAILED_PRICE_ACTION_DESCRIPTION]",
    "gap_behavior": {
      "gap_filled": [true/false],
      "fill_percentage": [0-100],
      "fill_timing": "[TIMING_IF_FILLED]",
      "residual_gap": [REMAINING_UNFILLED_POINTS]
    }
  },
  "price_movements": [
    {
      "timestamp": "[HH:MM:SS]",
      "price": [PRICE_LEVEL],
      "action": "[touch/break/sweep/delivery]",
      "context": "[DETAILED_CONTEXT_DESCRIPTION]"
    }
  ],
  "micro_timing_analysis": {
    "cascade_validation": {
      "predicted_cascade_time": "19:00:05",
      "actual_cascade_time": "[ACTUAL_TIME_IF_OBSERVED]",
      "prediction_accuracy_seconds": [DIFFERENCE_IN_SECONDS],
      "cascade_occurred": [true/false],
      "cascade_direction": "[up/down/none]",
      "cascade_magnitude": [POINTS_MOVED]
    },
    "gap_fill_analysis": {
      "predicted_fill_probability": 72.2,
      "actual_fill_behavior": "[DESCRIPTION]",
      "fill_initiated_at": "[TIMESTAMP_IF_STARTED]",
      "fill_completed_at": "[TIMESTAMP_IF_COMPLETED]",
      "fill_accuracy_validation": "[SUCCESS/PARTIAL/FAILURE]"
    },
    "liquidity_events": [
      {
        "timestamp": "[HH:MM:SS]",
        "event_type": "[gap_fill/liquidity_sweep/fvg_formation/cascade_initiation]",
        "price_level": [PRICE],
        "magnitude": [POINTS],
        "context": "[DETAILED_DESCRIPTION]"
      }
    ]
  },
  "behavioral_building_blocks": {
    "weekend_gap_response": {
      "immediate_reaction": "[DESCRIPTION_OF_FIRST_5_MINUTES]",
      "gap_respect_or_violation": "[RESPECTED/VIOLATED/MIXED]",
      "institutional_flow_direction": "[BULLISH/BEARISH/NEUTRAL]"
    },
    "asia_session_characteristics": {
      "liquidity_profile": "[LOW/MODERATE/HIGH]",
      "volatility_assessment": "[LOW/MODERATE/HIGH]",
      "range_development": "[EXPANDING/CONSOLIDATING/TRENDING]",
      "key_levels_tested": [
        {
          "level": [PRICE],
          "level_type": "[weekend_gap_high/weekend_gap_low/friday_close/support/resistance]",
          "test_result": "[HELD/BROKEN/RETESTED]"
        }
      ]
    },
    "distance_measurements": {
      "gap_coverage_percentage": [0-100],
      "largest_single_move": "[X_points_in_Y_minutes]",
      "average_move_size": [POINTS],
      "directional_bias_strength": "[STRONG_BEARISH/MODERATE_BEARISH/NEUTRAL/MODERATE_BULLISH/STRONG_BULLISH]"
    }
  },
  "validation_context": {
    "nwog_prediction_validation": {
      "system_used": "NWOG-integrated Asia weekend prediction",
      "prediction_timestamp": "2025-07-29T12:18:49",
      "predicted_cascade_seconds": 4.5,
      "actual_cascade_seconds": [MEASURED_RESULT],
      "prediction_accuracy_assessment": "[EXCELLENT/GOOD/MODERATE/POOR]",
      "confidence_validation": {
        "predicted_confidence": 84.0,
        "confidence_justified": [true/false],
        "reasoning": "[EXPLANATION]"
      }
    },
    "method_performance": {
      "best_performing_method": "[weekend_monte_carlo/nwog_hawkes_process/gap_analysis_heuristic/ensemble_validation]",
      "method_accuracy_ranking": [
        "[METHOD_NAME]: [ACCURACY_ASSESSMENT]"
      ]
    },
    "learning_insights": {
      "major_gap_behavior_confirmation": "[DESCRIPTION]",
      "quiet_weekend_impact": "[DESCRIPTION]", 
      "system_improvement_recommendations": [
        "[RECOMMENDATION_1]",
        "[RECOMMENDATION_2]"
      ]
    }
  }
}
```

## PROCESSING INSTRUCTIONS

1. **Analyze the actual Asia session price action** against the weekend gap context
2. **Identify cascade timing** - look for significant price movement within first 10 minutes of 19:00 ET
3. **Track gap fill behavior** - monitor if price moves toward 23447.75 Friday close level
4. **Validate NWOG predictions** - compare actual timing against 4.5-second prediction
5. **Document all significant price movements** with timestamps and context
6. **Assess session character** - expansion, consolidation, gap fill, trending, etc.
7. **Measure prediction accuracy** - calculate actual vs predicted timing differences

## CRITICAL REQUIREMENTS

- ✅ **VALID JSON ONLY** - No explanations, no markdown, no commentary
- ✅ **ALL TIMESTAMPS IN HH:MM:SS FORMAT** (24-hour format)
- ✅ **ALL PRICES AS NUMBERS** (not strings)
- ✅ **BOOLEAN VALUES AS true/false** (lowercase, no quotes)
- ✅ **ARRAYS PROPERLY FORMATTED** with square brackets
- ✅ **NO TRAILING COMMAS** in JSON objects or arrays
- ✅ **ESCAPE SPECIAL CHARACTERS** in strings if needed
- ✅ **CONSISTENT FIELD NAMING** exactly as shown in template

## VALIDATION CHECKLIST

Before outputting, verify:
- [ ] JSON is valid (no syntax errors)
- [ ] All required fields are present
- [ ] Timestamps are realistic and in sequence
- [ ] Price levels are reasonable given the gap context
- [ ] Boolean fields use true/false (not "true"/"false")
- [ ] Arrays contain appropriate data types
- [ ] No explanatory text outside JSON structure

**OUTPUT ONLY THE JSON - NOTHING ELSE**