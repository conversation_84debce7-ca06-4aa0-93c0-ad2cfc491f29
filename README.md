# Grok-Claude Computational Automation

Automated pipeline for GrokFour's 4-unit sequential calculation system (A→B→C→D), eliminating manual copy-pasting between computational stages.

## Overview

This system automates the complex mathematical pipeline that processes trading session data through 4 sequential computational units:

- **Unit A**: Foundation Calculations (equations 1-4, 8-11, 48-60)
- **Unit B**: Energy & Structure (equations 16-22, 32-38)  
- **Unit C**: Advanced Dynamics (equations 12-15, 23-31)
- **Unit D**: Integration & Validation (equations 5-7, 39-47)

**Total**: 78 mathematical equations processed automatically in <500ms

## Quick Start

### 1. Setup
```bash
cd grok-claude-automation
pip install -r requirements.txt
export GROK_API_KEY=xai-your-api-key-here
```

### 2. Test Connection
```bash
python cli.py test
```

### 3. Process Single Session
```bash
python cli.py process session.json micro_timing.json -o results.json
```

### 4. Batch Process Multiple Sessions
```bash
python cli.py batch sessions/ micro_timings/ -o results/
```

## Usage Examples

### Single Session Processing
```python
from src.pipeline import GrokPipeline

# Initialize pipeline
pipeline = GrokPipeline(api_key="your-grok-key")

# Process session
results = pipeline.process_session(session_data, micro_timing_data)

# Get summary
summary = pipeline.get_summary()
print(f"Processed in {summary['total_time_ms']}ms")
```

## Input Data Format

### Session Data Structure
```json
{
  "session_metadata": {
    "session_name": "asia_2025_07_23",
    "timeframe": "1min"
  },
  "price_data": {
    "open": 5850.0,
    "high": 5875.0,
    "low": 5840.0,
    "close": 5860.0
  },
  "behavioral_building_blocks": {},
  "htf_context": {},
  "timing_data": {}
}
```

## Output Structure

```json
{
  "grok_enhanced_calculations": {
    "unit_a_foundation": {
      "hybrid_volume": {
        "alpha_t": 0.73,
        "v_synthetic": 142.5,
        "v_hybrid": 149.3
      },
      "time_dilation_base": {
        "gamma_base": 1.45
      }
    },
    "unit_b_energy_structure": { /* ... */ },
    "unit_c_advanced_dynamics": { /* ... */ },
    "unit_d_integration_validation": { /* ... */ }
  },
  "pipeline_metadata": {
    "total_processing_time_ms": 280,
    "pipeline_status": "complete",
    "equations_processed": 78
  }
}
```

## Error Handling

The system includes comprehensive error handling:

- **Unit-level errors**: Individual units can fail with fallback values
- **Pipeline errors**: Complete pipeline failure with diagnostic information
- **API errors**: Grok CLI connection issues handled gracefully
- **Validation errors**: Input/output schema validation

## Performance

- **Total Processing Time**: <500ms for complete pipeline
- **Individual Unit Times**: A(45ms) + B(78ms) + C(92ms) + D(65ms)
- **Equations Processed**: 78 mathematical equations
- **Concurrent Processing**: Units process in strict sequence due to dependencies

## Architecture

```
Session JSON → Unit A → Unit B → Unit C → Unit D → Enhanced Results
     ↓           ↓        ↓        ↓        ↓
  Micro     Foundation  Energy  Advanced Integration
  Timing      Calcs    Structure Dynamics  Validation
```

## CLI Commands

```bash
# Process single session
python cli.py process session.json micro_timing.json -o results.json

# Batch process directory
python cli.py batch sessions/ micro_timings/ -o results/

# Test API connection  
python cli.py test

# Help
python cli.py --help
```

## Dependencies

- `requests>=2.28.0` - HTTP requests
- `pydantic>=2.0.0` - Data validation
- `pyyaml>=6.0` - Configuration files
- `python-dotenv>=1.0.0` - Environment variables

## Requirements

- Python 3.8+
- Grok CLI installed and configured
- Valid Grok API key (xAI account)
- JSON input files with proper schemas

## Production Systems

### News-Integrated Monte Carlo System
- **Performance**: 0.39 min average error (97.5% improvement)
- **News Integration**: Forex Factory impact levels mapped to timing multipliers
- **Usage**: `python3 grok_monte_carlo_package.py`

### Hawkes Process Cascade Prediction
- **Performance**: 0.0 minute error cascade timing
- **Method**: Self-exciting point process with ICT event sequences
- **Usage**: `python3 src/hawkes_cascade_predictor.py`

### Preprocessing Agent
- **Function**: Automated session preprocessing with selective JSON extraction
- **Performance**: <300 seconds per session with guaranteed tracker file generation
- **Usage**: `from src.preprocessing_agent import create_preprocessing_agent`

## JSON File Handling

### Migration-Safe Operations
The entire codebase uses centralized JSON functions that provide:
- **Smart path resolution** - automatically finds files in new locations
- **Backward compatibility** - old file paths work seamlessly
- **Future migration protection** - system won't break with file reorganization

### Usage
```python
# Always use these standardized functions instead of direct open() calls
from src.utils import load_json_data, save_json_data

# Load JSON data (automatically handles migration paths)
session_data = load_json_data('ASIA_grokEnhanced_2025_07_25.json')

# Save JSON data (automatic directory creation)
save_json_data(results, 'output_file.json')
```

## Troubleshooting

### Common Issues

**"GROK_API_KEY not found"**
- Set environment variable: `export GROK_API_KEY=your-key`
- Or use `-k` flag: `python cli.py test -k your-key`

**"Grok CLI error"**
- Verify Grok CLI is installed: `grok --help`
- Check API key is valid
- Test connection: `python cli.py test`

**"Invalid JSON response"**
- Input data may not match expected schema
- Check Unit system prompts for required format
- Enable debug mode for detailed error messages

## Development

To extend the system:

1. **Add new equations**: Modify unit system prompts
2. **Create new units**: Follow the `ComputationalUnit` base class pattern
3. **Enhance pipeline**: Modify `GrokPipeline` orchestration logic
4. **Add validation**: Extend error handling in individual units
5. **⚠️ ALWAYS use standardized JSON functions**: Use `load_json_data`/`save_json_data` instead of direct `open()` calls